import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';

// GET /api/students/lookup?admission_number=XXXXXXX - Lookup student by admission number
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const admissionNumber = searchParams.get('admission_number');

    // Validate admission number is provided
    if (!admissionNumber) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Admission number is required' 
        },
        { status: 400 }
      );
    }

    // Validate admission number format: 7-9 characters, first 2 digits, uppercase letters, last 3 digits
    const admissionNumberRegex = /^\d{2}[A-Z]{2,6}\d{3}$/;
    if (!admissionNumberRegex.test(admissionNumber.toUpperCase())) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid admission number format. Expected format: 2 digits + uppercase letters + 3 digits (e.g., 24CS094, 24MCA094, 25IMCA121)' 
        },
        { status: 400 }
      );
    }

    // Sanitize input (convert to uppercase for consistency)
    const sanitizedAdmissionNumber = admissionNumber.toUpperCase();

    // Query student from database
    const { data: student, error } = await supabaseAdmin
      .from('students')
      .select('id, name, admission_number, email, hostel, route')
      .eq('admission_number', sanitizedAdmissionNumber)
      .single();

    if (error) {
      // Check if it's a "not found" error
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { 
            success: false,
            error: 'Student not found with this admission number' 
          },
          { status: 404 }
        );
      }
      
      console.error('Database error:', error);
      return NextResponse.json(
        { 
          success: false,
          error: 'Failed to lookup student' 
        },
        { status: 500 }
      );
    }

    // Return student data
    return NextResponse.json({
      success: true,
      data: {
        id: student.id,
        name: student.name,
        admissionNumber: student.admission_number,
        // Ensure email and routeCode are never null to avoid NOT NULL constraint errors downstream
        email: student.email ?? '',
        hostel: student.hostel,
        routeCode: student.route ?? ''
      }
    });

  } catch (error) {
    console.error('Student lookup error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error' 
      },
      { status: 500 }
    );
  }
}
