import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/middleware';
import { supabaseAdmin } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  return withAuth(request, async (req) => {
    try {
      const { searchParams } = new URL(request.url);
      const startDate = searchParams.get('start_date');
      const endDate = searchParams.get('end_date');

      // Validate required parameters
      if (!startDate || !endDate) {
        return NextResponse.json({
          success: false,
          error: 'start_date and end_date parameters are required',
          data: null
        }, { status: 400 });
      }

      // Validate date format and range
      const start = new Date(startDate);
      const end = new Date(endDate);
      
      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return NextResponse.json({
          success: false,
          error: 'Invalid date format. Use YYYY-MM-DD format',
          data: null
        }, { status: 400 });
      }

      if (start > end) {
        return NextResponse.json({
          success: false,
          error: 'start_date cannot be after end_date',
          data: null
        }, { status: 400 });
      }

      // Get bookings data within date range
      const { data: bookingsData, error: bookingsError } = await supabaseAdmin
        .from('bookings')
        .select(`
          bus_route,
          fare,
          payment_status,
          created_at
        `)
        .gte('created_at', startDate + 'T00:00:00.000Z')
        .lte('created_at', endDate + 'T23:59:59.999Z')
        .eq('payment_status', true); // Only paid bookings for revenue

      if (bookingsError) {
        console.error('Error fetching bookings for revenue:', bookingsError);
        return NextResponse.json({
          success: false,
          error: 'Failed to fetch bookings data',
          data: null
        }, { status: 500 });
      }

      // Get bus information for route names
      const { data: busData, error: busError } = await supabaseAdmin
        .from('buses')
        .select('route_code, name')
        .eq('is_active', true);

      if (busError) {
        console.error('Error fetching bus data:', busError);
        return NextResponse.json({
          success: false,
          error: 'Failed to fetch bus data',
          data: null
        }, { status: 500 });
      }

      // Create a map of route codes to bus names
      const routeNameMap = busData?.reduce((acc: any, bus: any) => {
        acc[bus.route_code] = bus.name;
        return acc;
      }, {}) || {};

      // Group bookings by route and calculate revenue
      const routeRevenue: { [key: string]: { totalRevenue: number; bookingCount: number; busName: string } } = {};

      bookingsData?.forEach((booking: any) => {
        const route = booking.bus_route;
        const fare = booking.fare || 0;

        if (!routeRevenue[route]) {
          routeRevenue[route] = {
            totalRevenue: 0,
            bookingCount: 0,
            busName: routeNameMap[route] || route
          };
        }

        routeRevenue[route].totalRevenue += fare;
        routeRevenue[route].bookingCount += 1;
      });

      // Transform to array format and calculate additional metrics
      const routes = Object.entries(routeRevenue).map(([busRoute, data]) => ({
        busRoute,
        busName: data.busName,
        totalRevenue: data.totalRevenue,
        bookingCount: data.bookingCount,
        revenuePerBooking: data.bookingCount > 0 ? data.totalRevenue / data.bookingCount : 0,
        percentageOfTotal: 0 // Will be calculated after total is known
      }));

      // Calculate total revenue
      const totalRevenue = routes.reduce((sum, route) => sum + route.totalRevenue, 0);
      const totalBookings = routes.reduce((sum, route) => sum + route.bookingCount, 0);

      // Calculate percentage of total for each route
      routes.forEach(route => {
        route.percentageOfTotal = totalRevenue > 0 ? (route.totalRevenue / totalRevenue) * 100 : 0;
      });

      // Sort by total revenue (highest first)
      routes.sort((a, b) => b.totalRevenue - a.totalRevenue);

      // Calculate daily revenue breakdown
      const dailyRevenue: { [key: string]: number } = {};
      bookingsData?.forEach((booking: any) => {
        const date = booking.created_at.split('T')[0];
        const fare = booking.fare || 0;
        dailyRevenue[date] = (dailyRevenue[date] || 0) + fare;
      });

      const dailyBreakdown = Object.entries(dailyRevenue)
        .map(([date, revenue]) => ({ date, revenue }))
        .sort((a, b) => a.date.localeCompare(b.date));

      const response = {
        success: true,
        data: {
          totalRevenue,
          totalBookings,
          averageRevenuePerBooking: totalBookings > 0 ? totalRevenue / totalBookings : 0,
          routes,
          dailyBreakdown,
          summary: {
            dateRange: {
              startDate,
              endDate
            },
            totalRoutes: routes.length,
            highestRevenueRoute: routes.length > 0 ? routes[0] : null,
            lowestRevenueRoute: routes.length > 0 ? routes[routes.length - 1] : null
          }
        }
      };

      return NextResponse.json(response, {
        headers: {
          'Cache-Control': 'max-age=60', // 1-minute cache for custom reports
          'Content-Type': 'application/json'
        }
      });

    } catch (error) {
      console.error('Unexpected error in custom revenue endpoint:', error);
      return NextResponse.json({
        success: false,
        error: 'Internal server error',
        data: null
      }, { status: 500 });
    }
  });
}
