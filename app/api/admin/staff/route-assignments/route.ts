export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from 'next/server';
import { withAdminAuth, getUser } from '@/lib/middleware';
import { supabaseAdmin } from '@/lib/supabase';
import { createApiResponse, handleApiError } from '@/lib/middleware';

// GET - Get route assignment status for all routes
export async function GET(request: NextRequest) {
  return withAdminAuth(request, async (req) => {
    try {
      const user = getUser(req);
      if (!user) {
        return NextResponse.json(
          { error: 'Admin authentication required' },
          { status: 401 }
        );
      }

      // Get route assignment status using the database function
      const { data: routeAssignments, error } = await supabaseAdmin
        .rpc('get_route_assignment_status');

      if (error) {
        console.error('Error fetching route assignments:', error);
        return NextResponse.json(
          { error: 'Failed to fetch route assignments' },
          { status: 500 }
        );
      }

      return createApiResponse(routeAssignments || []);
    } catch (error) {
      return handleApiError(error, 'Failed to fetch route assignments');
    }
  });
} 