import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { withAuth } from '@/lib/middleware';

export async function POST(request: NextRequest) {
  return withAuth(request, async (req) => {
    try {
      // Use the simplified database function to reset current bookings and bus seats
      // This will:
      // 1. Delete all records from current_bookings table
      // 2. Reset available_seats = total_seats for all active buses
      // 3. Provide comprehensive logging and error handling

      // Call the database function to reset all booking statistics and bus seats
      const { data, error: resetError } = await supabaseAdmin
        .rpc('reset_all_bookings', {});

      if (resetError) {
        console.error('Error calling reset_all_bookings function:', resetError);
        console.error('Error details:', {
          message: resetError.message,
          code: resetError.code,
          details: resetError.details,
          hint: resetError.hint
        });
        
        return NextResponse.json({
          success: false,
          error: 'Failed to reset booking statistics and bus seats',
          details: resetError.message || 'Unknown error',
          code: resetError.code,
          data: null
        }, { status: 500 });
      }

      return NextResponse.json({
        success: true,
        data: {
          message: 'All booking data reset successfully using database functions',
          deletedBookings: true,
          resetSeats: true,
          resetStatistics: true
        },
        error: null
      });
    } catch (error) {
      console.error('Unexpected error resetting booking data:', error);
      return NextResponse.json({
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
        data: null
      }, { status: 500 });
    }
  });
}
