import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { withAuth, createApiResponse, handleApiError } from '@/lib/middleware';

// GET /api/admin/current-bookings - Get all current bookings with pagination
export async function GET(request: NextRequest) {
  return withAuth(request, async (req) => {
    try {
      const { searchParams } = new URL(req.url);
      const page = parseInt(searchParams.get('page') || '1');
      const limit = parseInt(searchParams.get('limit') || '50');

      const offset = (page - 1) * limit;

      let query = supabaseAdmin
        .from('current_bookings')
        .select('*', { count: 'exact' })
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      const { data: bookings, error, count } = await query;

      if (error) {
        throw error;
      }

      return createApiResponse({
        bookings,
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit)
        }
      });
    } catch (error) {
      return handleApiError(error, 'Failed to fetch current bookings');
    }
  });
}
