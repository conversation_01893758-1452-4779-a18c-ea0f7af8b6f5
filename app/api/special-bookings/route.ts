import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { checkAdminAuth } from '@/lib/auth-utils';
import { generateBookingId } from '@/lib/utils';

export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const adminUser = await checkAdminAuth(request);
    if (!adminUser) {
      return NextResponse.json({ 
        success: false, 
        error: 'Admin authentication required' 
      }, { status: 401 });
    }

    const body = await request.json();
    const {
      studentName,
      admissionNumber,
      busRoute,
      destination,
      paymentStatus,
      goDate,
      returnDate,
      fare,
      busName,
      isSpecialBooking,
      razorpay_payment_id,
      razorpay_order_id,
      razorpay_signature,
    } = body;

    console.log('Creating special booking with data:', {
      studentName,
      admissionNumber,
      busRoute,
      destination,
      paymentStatus,
      goDate,
      returnDate,
      fare,
      busName,
      isSpecialBooking,
      adminUser: adminUser.username,
      hasRazorpayData: !!(razorpay_payment_id || razorpay_order_id || razorpay_signature),
    });

    // Validate required fields
    if (!studentName || !admissionNumber || !busRoute || !destination || !goDate || !returnDate || !fare || !busName) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields'
      }, { status: 400 });
    }

    // Generate unique booking ID for special booking
    let booking_id: string = '';
    let isUnique = false;
    let attempts = 0;
    const maxAttempts = 5;

    // Retry logic to ensure unique booking ID
    while (!isUnique && attempts < maxAttempts) {
      try {
        booking_id = generateBookingId(busRoute, destination);

        // Check if booking_id already exists in either table
        const { data: existingBooking, error: checkError } = await supabaseAdmin
          .from('bookings')
          .select('booking_id')
          .eq('booking_id', booking_id)
          .single();

        if (checkError && checkError.code === 'PGRST116') {
          // No existing booking found - ID is unique
          isUnique = true;
        } else if (existingBooking) {
          // Booking ID already exists, try again
          attempts++;
          console.log(`Booking ID ${booking_id} already exists, retrying... (attempt ${attempts})`);
        } else if (checkError) {
          // Other database error
          throw checkError;
        }
      } catch (error) {
        console.error('Error generating booking ID:', error);
        return NextResponse.json({
          success: false,
          error: 'Failed to generate unique booking ID',
          details: 'Please try again'
        }, { status: 500 });
      }
    }

    if (!isUnique) {
      return NextResponse.json({
        success: false,
        error: 'Unable to generate unique booking ID',
        details: 'Please try again later'
      }, { status: 500 });
    }

    console.log(`Generated unique booking ID: ${booking_id} for special booking`);

    // Validate student name format (2-3 words)
    const nameParts = studentName.trim().split(/\s+/);
    if (nameParts.length < 2 || nameParts.length > 3) {
      return NextResponse.json({
        success: false,
        error: 'Student name must contain 2-3 words (first name + surname + optional middle name)'
      }, { status: 400 });
    }

    // Validate admission number format
    const admissionRegex = /^[0-9]{2}[A-Z]{2,4}[0-9]{3}$/;
    if (!admissionRegex.test(admissionNumber)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid admission number format. Expected: 2 digits + 2-4 letters + 3 digits'
      }, { status: 400 });
    }

    // Validate dates
    const goDateObj = new Date(goDate);
    const returnDateObj = new Date(returnDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (goDateObj < today) {
      return NextResponse.json({
        success: false,
        error: 'Go date cannot be in the past'
      }, { status: 400 });
    }

    if (returnDateObj <= goDateObj) {
      return NextResponse.json({
        success: false,
        error: 'Return date must be after go date'
      }, { status: 400 });
    }

    // Verify bus exists and is active
    const { data: busData, error: busError } = await supabaseAdmin
      .from('buses')
      .select('id, name, route_code, is_active')
      .eq('route_code', busRoute)
      .eq('is_active', true)
      .single();

    if (busError || !busData) {
      console.error('Bus verification failed:', busError);
      return NextResponse.json({
        success: false,
        error: 'Invalid or inactive bus route'
      }, { status: 400 });
    }

    // Verify route stop exists and is active
    const { data: routeStop, error: stopError } = await supabaseAdmin
      .from('route_stops')
      .select('id, stop_name, fare, is_active')
      .eq('route_code', busRoute)
      .eq('stop_name', destination)
      .eq('is_active', true)
      .single();

    if (stopError || !routeStop) {
      console.error('Route stop verification failed:', stopError);
      return NextResponse.json({
        success: false,
        error: 'Invalid destination for the selected route'
      }, { status: 400 });
    }

    // Verify fare matches
    if (routeStop.fare !== fare) {
      return NextResponse.json({
        success: false,
        error: 'Fare mismatch. Please refresh and try again.'
      }, { status: 400 });
    }

    // Check for duplicate booking (same student, same dates, same route)
    const { data: existingBooking, error: duplicateError } = await supabaseAdmin
      .from('bookings')
      .select('id')
      .eq('admission_number', admissionNumber)
      .eq('go_date', goDate)
      .eq('return_date', returnDate)
      .eq('bus_route', busRoute)
      .single();

    if (existingBooking) {
      return NextResponse.json({
        success: false,
        error: 'A booking already exists for this student with the same dates and route'
      }, { status: 409 });
    }

    // Create the special booking
    const bookingData = {
      booking_id: booking_id, // Generated unique booking ID
      admission_number: admissionNumber,
      student_name: studentName,
      bus_route: busRoute,
      destination: destination,
      payment_status: paymentStatus,
      go_date: goDate,
      return_date: returnDate,
      fare: fare,
      bus_name: busName,
      is_special: true,
      razorpay_payment_id: razorpay_payment_id || null,
      razorpay_order_id: razorpay_order_id || null,
      razorpay_signature: razorpay_signature || null,
    };

    // Insert into bookings table only (special bookings don't go into current_bookings)
    const { data: booking, error: bookingError } = await supabaseAdmin
      .from('bookings')
      .insert([bookingData])
      .select()
      .single();

    if (bookingError) {
      console.error('Failed to create special booking:', bookingError);
      console.error('Booking data that failed:', bookingData);

      // Check if it's a unique constraint violation for booking_id
      if (bookingError.code === '23505' && bookingError.message.includes('booking_id')) {
        return NextResponse.json({
          success: false,
          error: 'Booking ID already exists',
          details: 'This booking ID has already been used. Please try again.'
        }, { status: 409 });
      }

      return NextResponse.json({
        success: false,
        error: `Failed to create booking: ${bookingError.message || 'Unknown error'}`
      }, { status: 500 });
    }

    console.log('Special booking created successfully:', {
      bookingId: booking_id,
      databaseId: booking.id,
      studentName,
      admissionNumber,
      busRoute,
      destination,
      paymentStatus,
      adminUser: adminUser.username
    });

    return NextResponse.json({
      success: true,
      data: {
        booking_id: booking_id, // Return the generated booking ID
        database_id: booking.id, // Also include database ID for reference
        student_name: studentName,
        admission_number: admissionNumber,
        bus_route: busRoute,
        destination: destination,
        go_date: goDate,
        return_date: returnDate,
        fare: fare,
        message: 'Special booking created successfully'
      }
    });

  } catch (error) {
    console.error('Special booking creation error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error. Please try again.'
    }, { status: 500 });
  }
}

// GET method to retrieve special bookings (admin only)
export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const adminUser = await checkAdminAuth(request);
    if (!adminUser) {
      return NextResponse.json({ 
        success: false, 
        error: 'Admin authentication required!!' 
      }, { status: 401 });
    }

    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    // Get special bookings with pagination
    const { data: bookings, error: bookingsError } = await supabaseAdmin
      .from('bookings')
      .select('*')
      .eq('is_special', true)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (bookingsError) {
      console.error('Failed to fetch special bookings:', bookingsError);
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch special bookings'
      }, { status: 500 });
    }

    // Get total count
    const { count, error: countError } = await supabaseAdmin
      .from('bookings')
      .select('*', { count: 'exact', head: true })
      .eq('is_special', true);

    if (countError) {
      console.error('Failed to get special bookings count:', countError);
    }

    return NextResponse.json({
      success: true,
      data: {
        bookings: bookings || [],
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit)
        }
      }
    });

  } catch (error) {
    console.error('Special bookings fetch error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}