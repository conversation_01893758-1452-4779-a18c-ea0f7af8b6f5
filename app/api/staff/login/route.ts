export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from 'next/server';
import { authenticateStaff, createStaffSession } from '@/lib/auth';
import { setStaffAuthCookie } from '@/lib/middleware';
import { createApiResponse, handleApiError } from '@/lib/middleware';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { username, password } = body;

    // Validate required fields
    if (!username || !password) {
      return NextResponse.json(
        { error: 'Username and password are required' },
        { status: 400 }
      );
    }

    // Authenticate staff user
    const staff = await authenticateStaff(username, password);
    if (!staff) {
      return NextResponse.json(
        { error: 'Invalid username or password' },
        { status: 401 }
      );
    }

    // Create staff session
    const session = await createStaffSession(staff);
    if (!session) {
      return NextResponse.json(
        { error: 'Failed to create session' },
        { status: 500 }
      );
    }

    // Create response with staff data
    const response = createApiResponse({
      staff: {
        id: staff.id,
        username: staff.username,
        route_code: staff.route_code
      },
      token: session.token
    });

    // Set authentication cookie
    setStaffAuthCookie(response, session.token);

    return response;
  } catch (error) {
    return handleApiError(error, 'Staff login failed');
  }
}
