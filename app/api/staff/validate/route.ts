export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from 'next/server';
import { validateStaffSession } from '@/lib/auth';
import { createApiResponse, handleApiError } from '@/lib/middleware';

export async function GET(request: NextRequest) {
  try {
    // Get token from cookie
    const token = request.cookies.get('staff_token')?.value;

    if (!token) {
      return createApiResponse({
        valid: false
      });
    }

    // Validate session
    const staff = await validateStaffSession(token);

    if (!staff) {
      return createApiResponse({
        valid: false
      });
    }

    return createApiResponse({
      valid: true,
      staff: {
        id: staff.id,
        username: staff.username,
        route_code: staff.route_code
      }
    });
  } catch (error) {
    return handleApiError(error, 'Staff session validation failed');
  }
}
