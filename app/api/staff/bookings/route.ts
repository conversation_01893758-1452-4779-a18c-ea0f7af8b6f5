export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from 'next/server';
import { withStaffAuth, getStaff } from '@/lib/middleware';
import { supabaseAdmin } from '@/lib/supabase';
import { createApiResponse, handleApiError } from '@/lib/middleware';

export async function GET(request: NextRequest) {
  return withStaffAuth(request, async (req) => {
    try {
      const staff = getStaff(req);
      if (!staff) {
        return NextResponse.json(
          { error: 'Staff authentication required' },
          { status: 401 }
        );
      }

      const { searchParams } = new URL(request.url);
      const routeCode = searchParams.get('route') || staff.route_code;

      // Verify staff can only access their own route data
      if (routeCode !== staff.route_code) {
        return NextResponse.json(
          { error: 'Access denied: You can only view bookings for your assigned route' },
          { status: 403 }
        );
      }

      // Fetch bookings for the staff's route
      const { data: bookings, error: bookingsError } = await supabaseAdmin
        .from('current_bookings')
        .select(`
          booking_id,
          admission_number,
          student_name,
          bus_route,
          destination,
          payment_status,
          created_at,
          go_date,
          return_date,
          fare,
          bus_name,
          boarded
        `)
        .eq('bus_route', routeCode)
        .order('created_at', { ascending: false });

      if (bookingsError) {
        console.error('Error fetching bookings:', bookingsError);
        return NextResponse.json(
          { error: 'Failed to fetch bookings' },
          { status: 500 }
        );
      }

      // Simplified statistics - only total bookings
      interface Booking {
        booking_id: string;
        admission_number: string;
        student_name: string;
        bus_route: string;
        destination: string;
        payment_status: boolean;
        created_at: string;
        go_date: string | null;
        return_date: string | null;
        fare: number | null;
        bus_name: string;
      }

      interface BookingStats {
        total: number;
      }

      const stats: BookingStats = {
        total: (bookings as Booking[])?.length || 0
      };

      return NextResponse.json({
        success: true,
        data: {
          bookings: bookings || [],
          stats,
          route_code: routeCode,
          bus_name: (bookings as Booking[])?.[0]?.bus_name || 'Unknown Bus',
          staff_info: {
            username: staff.username,
            route_code: staff.route_code
          }
        }
      });
    } catch (error) {
      return handleApiError(error, 'Failed to fetch staff bookings');
    }
  });
}
