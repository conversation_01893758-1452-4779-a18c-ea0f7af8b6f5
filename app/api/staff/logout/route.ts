export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from 'next/server';
import { invalidateStaffSession } from '@/lib/auth';
import { createApiResponse, handleApiError, clearStaffAuthCookie } from '@/lib/middleware';

export async function POST(request: NextRequest) {
  try {
    // Get token from cookie
    const token = request.cookies.get('staff_token')?.value;

    if (token) {
      // Invalidate session in database
      await invalidateStaffSession(token);
    }

    // Create response
    const response = NextResponse.json(
      createApiResponse({ message: 'Logged out successfully' })
    );

    // Clear authentication cookie
    clearStaffAuthCookie(response);

    return response;
  } catch (error) {
    return handleApiError(error, 'Staff logout failed');
  }
}
