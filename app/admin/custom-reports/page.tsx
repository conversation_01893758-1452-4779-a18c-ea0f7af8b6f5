'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { withAdminAuth } from '@/contexts/AdminContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RefreshCw, FileText, Home, Calendar } from 'lucide-react';
import { toast } from 'sonner';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';

// Import tab components (will create these next)
import BookingsTab from './components/BookingsTab';
import RevenueTab from './components/RevenueTab';
import RouteDemandTab from './components/RouteDemandTab';

interface DateRange {
  startDate: string;
  endDate: string;
}

interface CustomReportsState {
  activeTab: 'bookings' | 'revenue' | 'route-demand';
  dateRange: DateRange;
  isDateRangeSet: boolean;
  refreshInfo: {
    lastUpdated: Date;
    isRefreshing: boolean;
  };
}

function CustomReportsPage() {
  const router = useRouter();
  const [state, setState] = useState<CustomReportsState>({
    activeTab: 'bookings',
    dateRange: {
      startDate: '',
      endDate: ''
    },
    isDateRangeSet: false,
    refreshInfo: {
      lastUpdated: new Date(),
      isRefreshing: false
    }
  });

  // Set default date range to last 30 days
  useEffect(() => {
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    setState(prev => ({
      ...prev,
      dateRange: {
        startDate: thirtyDaysAgo.toISOString().split('T')[0],
        endDate: today.toISOString().split('T')[0]
      }
    }));
  }, []);

  const handleDateRangeSubmit = useCallback(() => {
    if (!state.dateRange.startDate || !state.dateRange.endDate) {
      toast.error('Please select both start and end dates');
      return;
    }

    if (new Date(state.dateRange.startDate) > new Date(state.dateRange.endDate)) {
      toast.error('Start date cannot be after end date');
      return;
    }

    setState(prev => ({
      ...prev,
      isDateRangeSet: true,
      refreshInfo: {
        lastUpdated: new Date(),
        isRefreshing: false
      }
    }));

    toast.success('Date range applied successfully');
  }, [state.dateRange]);

  const handleTabChange = useCallback((tab: string) => {
    setState(prev => ({ ...prev, activeTab: tab as any }));
  }, []);

  const handleDateChange = useCallback((field: 'startDate' | 'endDate', value: string) => {
    setState(prev => ({
      ...prev,
      dateRange: {
        ...prev.dateRange,
        [field]: value
      },
      isDateRangeSet: false // Reset when dates change
    }));
  }, []);

  const refreshData = useCallback(() => {
    if (!state.isDateRangeSet) {
      toast.error('Please apply a date range first');
      return;
    }

    setState(prev => ({
      ...prev,
      refreshInfo: {
        lastUpdated: new Date(),
        isRefreshing: true
      }
    }));

    // Simulate refresh delay
    setTimeout(() => {
      setState(prev => ({
        ...prev,
        refreshInfo: {
          ...prev.refreshInfo,
          isRefreshing: false
        }
      }));
      toast.success('Data refreshed successfully');
    }, 1000);
  }, [state.isDateRangeSet]);

  return (
    <div className="min-h-screen p-3 sm:p-4 lg:p-6 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4 mb-6 lg:mb-8"
        >
          <div className="flex-1">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-2">Custom Reports</h1>
            <p className="text-sm sm:text-base text-gray-600">
              Generate custom reports for specific date ranges
            </p>
          </div>
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4">
            <Button
              onClick={() => router.push('/admin/dashboard')}
              variant="ghost"
              className="flex items-center justify-center gap-2 h-10 sm:h-auto"
            >
              <Home className="w-4 h-4" />
              <span className="sm:inline">Back to Dashboard</span>
            </Button>
            <Button
              onClick={refreshData}
              disabled={state.refreshInfo.isRefreshing || !state.isDateRangeSet}
              variant="outline"
              className="flex items-center justify-center gap-2 h-10 sm:h-auto"
            >
              <RefreshCw className={`w-4 h-4 ${state.refreshInfo.isRefreshing ? 'animate-spin' : ''}`} />
              <span className="sm:inline">Refresh</span>
            </Button>
          </div>
        </motion.div>

        {/* Date Range Selector */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="mb-6 lg:mb-8"
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                <Calendar className="w-4 h-4 sm:w-5 sm:h-5" />
                Select Date Range
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 items-end">
                <div className="space-y-2">
                  <Label htmlFor="start-date" className="text-sm font-medium">Start Date</Label>
                  <Input
                    id="start-date"
                    type="date"
                    value={state.dateRange.startDate}
                    onChange={(e) => handleDateChange('startDate', e.target.value)}
                    className="h-10 text-base"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="end-date" className="text-sm font-medium">End Date</Label>
                  <Input
                    id="end-date"
                    type="date"
                    value={state.dateRange.endDate}
                    onChange={(e) => handleDateChange('endDate', e.target.value)}
                    className="h-10 text-base"
                  />
                </div>
                <div className="sm:col-span-2 lg:col-span-1">
                  <Button
                    onClick={handleDateRangeSubmit}
                    className="w-full h-10 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white text-base font-medium"
                  >
                    Apply Date Range
                  </Button>
                </div>
              </div>
              {state.isDateRangeSet && (
                <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-green-800 text-sm sm:text-base">
                    <strong>Active Date Range:</strong> {state.dateRange.startDate} to {state.dateRange.endDate}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Reports Content */}
        {state.isDateRangeSet ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                  <FileText className="w-4 h-4 sm:w-5 sm:h-5" />
                  Custom Reports Dashboard
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs value={state.activeTab} onValueChange={handleTabChange}>
                  <TabsList className="grid w-full grid-cols-1 sm:grid-cols-3 h-auto sm:h-10 gap-1 sm:gap-0 p-1">
                    <TabsTrigger
                      value="bookings"
                      className="h-10 sm:h-auto text-sm sm:text-base font-medium data-[state=active]:bg-background data-[state=active]:text-foreground"
                    >
                      Bookings
                    </TabsTrigger>
                    <TabsTrigger
                      value="revenue"
                      className="h-10 sm:h-auto text-sm sm:text-base font-medium data-[state=active]:bg-background data-[state=active]:text-foreground"
                    >
                      Revenue
                    </TabsTrigger>
                    <TabsTrigger
                      value="route-demand"
                      className="h-10 sm:h-auto text-sm sm:text-base font-medium data-[state=active]:bg-background data-[state=active]:text-foreground"
                    >
                      Route Demand
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="bookings" className="mt-6">
                    <BookingsTab
                      dateRange={state.dateRange}
                      isRefreshing={state.refreshInfo.isRefreshing}
                    />
                  </TabsContent>

                  <TabsContent value="revenue" className="mt-6">
                    <RevenueTab
                      dateRange={state.dateRange}
                      isRefreshing={state.refreshInfo.isRefreshing}
                    />
                  </TabsContent>

                  <TabsContent value="route-demand" className="mt-6">
                    <RouteDemandTab
                      dateRange={state.dateRange}
                      isRefreshing={state.refreshInfo.isRefreshing}
                    />
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-center py-12"
          >
            <Card>
              <CardContent className="py-8 sm:py-12">
                <Calendar className="w-12 h-12 sm:w-16 sm:h-16 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg sm:text-xl font-semibold text-gray-600 mb-2">
                  Select a Date Range
                </h3>
                <p className="text-sm sm:text-base text-gray-500 px-4">
                  Please select a start and end date above to generate your custom reports.
                </p>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </div>
    </div>
  );
}

export default withAdminAuth(CustomReportsPage);
