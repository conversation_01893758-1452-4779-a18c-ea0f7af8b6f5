'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
// import { Progress } from '@/components/ui/progress';
import { Download, TrendingUp, Users, Bus, Target } from 'lucide-react';
import { toast } from 'sonner';

interface DateRange {
  startDate: string;
  endDate: string;
}

interface RouteInfo {
  routeCode: string;
  busName: string;
  totalBookings: number;
  capacity: number;
  utilizationPercentage: number;
  demandLevel: 'High' | 'Medium' | 'Low';
  availableSeats: number;
  isActive: boolean;
}

interface DailyTrend {
  date: string;
  totalBookings: number;
  routeBreakdown: { [route: string]: number };
}

interface RouteDemandData {
  routes: RouteInfo[];
  dailyTrends: DailyTrend[];
  summary: {
    dateRange: {
      startDate: string;
      endDate: string;
    };
    totalRoutes: number;
    totalBookings: number;
    totalCapacity: number;
    overallUtilization: number;
    demandDistribution: {
      high: number;
      medium: number;
      low: number;
    };
    highestDemandRoute: RouteInfo | null;
    lowestDemandRoute: RouteInfo | null;
  };
}

interface RouteDemandTabProps {
  dateRange: DateRange;
  isRefreshing: boolean;
}

export default function RouteDemandTab({ dateRange, isRefreshing }: RouteDemandTabProps) {
  const [data, setData] = useState<RouteDemandData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchRouteDemandData = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        start_date: dateRange.startDate,
        end_date: dateRange.endDate
      });

      const response = await fetch(`/api/admin/custom-reports/route-demand?${params}`, {
        credentials: 'include'
      });

      const result = await response.json();

      if (result.success) {
        setData(result.data);
      } else {
        setError(result.error || 'Failed to fetch route demand data');
        toast.error('Failed to fetch route demand data');
      }
    } catch (error) {
      setError('Network error while fetching route demand data');
      toast.error('Network error while fetching route demand data');
      console.error('Route demand fetch error:', error);
    } finally {
      setIsLoading(false);
    }
  }, [dateRange]);

  useEffect(() => {
    fetchRouteDemandData();
  }, [fetchRouteDemandData]);

  const handleExport = () => {
    if (!data) return;
    
    const headers = ['Route Code', 'Bus Name', 'Total Bookings', 'Capacity', 'Utilization %', 'Demand Level', 'Available Seats'];
    const csvContent = [
      headers.join(','),
      ...data.routes.map(route => [
        route.routeCode,
        `"${route.busName}"`,
        route.totalBookings,
        route.capacity,
        route.utilizationPercentage.toFixed(2) + '%',
        route.demandLevel,
        route.availableSeats
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `route-demand-${dateRange.startDate}-to-${dateRange.endDate}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    
    toast.success('Route demand data exported successfully');
  };

  const getDemandBadgeColor = (level: 'High' | 'Medium' | 'Low') => {
    switch (level) {
      case 'High':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'Medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading route demand data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={fetchRouteDemandData} variant="outline">
          Try Again
        </Button>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">No route demand data available</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4 sm:p-6">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-xs sm:text-sm font-medium text-gray-600">Total Routes</p>
                <p className="text-xl sm:text-2xl font-bold text-blue-600">{data.summary.totalRoutes}</p>
              </div>
              <Bus className="h-6 w-6 sm:h-8 sm:w-8 text-blue-600 ml-auto flex-shrink-0" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 sm:p-6">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-xs sm:text-sm font-medium text-gray-600">Total Bookings</p>
                <p className="text-xl sm:text-2xl font-bold text-green-600">{data.summary.totalBookings}</p>
              </div>
              <Users className="h-6 w-6 sm:h-8 sm:w-8 text-green-600 ml-auto flex-shrink-0" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 sm:p-6">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-xs sm:text-sm font-medium text-gray-600">Total Capacity</p>
                <p className="text-xl sm:text-2xl font-bold text-purple-600">{data.summary.totalCapacity}</p>
              </div>
              <Target className="h-6 w-6 sm:h-8 sm:w-8 text-purple-600 ml-auto flex-shrink-0" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 sm:p-6">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-xs sm:text-sm font-medium text-gray-600">Overall Utilization</p>
                <p className="text-xl sm:text-2xl font-bold text-orange-600">{data.summary.overallUtilization}%</p>
              </div>
              <TrendingUp className="h-6 w-6 sm:h-8 sm:w-8 text-orange-600 ml-auto flex-shrink-0" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 sm:p-6">
            <div className="text-center">
              <p className="text-xs sm:text-sm font-medium text-gray-600 mb-2">Demand Distribution</p>
              <div className="space-y-1">
                <div className="flex justify-between text-xs">
                  <span className="text-red-600">High: {data.summary.demandDistribution.high}</span>
                  <span className="text-yellow-600">Med: {data.summary.demandDistribution.medium}</span>
                  <span className="text-green-600">Low: {data.summary.demandDistribution.low}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top and Bottom Performing Routes */}
      {data.summary.highestDemandRoute && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base sm:text-lg text-red-600">Highest Demand Route</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p className="text-sm sm:text-base font-semibold">{data.summary.highestDemandRoute.busName}</p>
                <p className="text-xs sm:text-sm text-gray-600">Route: {data.summary.highestDemandRoute.routeCode}</p>
                <p className="text-base sm:text-lg font-bold text-red-600">{data.summary.highestDemandRoute.totalBookings} bookings</p>
                <p className="text-xs sm:text-sm text-gray-600">Utilization: {data.summary.highestDemandRoute.utilizationPercentage}%</p>
                <Badge className={getDemandBadgeColor(data.summary.highestDemandRoute.demandLevel)}>
                  {data.summary.highestDemandRoute.demandLevel} Demand
                </Badge>
              </div>
            </CardContent>
          </Card>
          {data.summary.lowestDemandRoute && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base sm:text-lg text-green-600">Lowest Demand Route</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p className="text-sm sm:text-base font-semibold">{data.summary.lowestDemandRoute.busName}</p>
                  <p className="text-xs sm:text-sm text-gray-600">Route: {data.summary.lowestDemandRoute.routeCode}</p>
                  <p className="text-base sm:text-lg font-bold text-green-600">{data.summary.lowestDemandRoute.totalBookings} bookings</p>
                  <p className="text-xs sm:text-sm text-gray-600">Utilization: {data.summary.lowestDemandRoute.utilizationPercentage}%</p>
                  <Badge className={getDemandBadgeColor(data.summary.lowestDemandRoute.demandLevel)}>
                    {data.summary.lowestDemandRoute.demandLevel} Demand
                  </Badge>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Route Demand Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
            <span className="text-lg sm:text-xl">Route Demand Analysis</span>
            <Button onClick={handleExport} variant="outline" size="sm" className="h-10 self-start sm:self-auto">
              <Download className="w-4 h-4 mr-2" />
              Export Data
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Desktop Table View */}
          <div className="hidden lg:block">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="min-w-[100px]">Route</TableHead>
                    <TableHead className="min-w-[150px]">Bus Name</TableHead>
                    <TableHead className="min-w-[80px]">Bookings</TableHead>
                    <TableHead className="min-w-[80px]">Capacity</TableHead>
                    <TableHead className="min-w-[100px]">Utilization</TableHead>
                    <TableHead className="min-w-[100px]">Available Seats</TableHead>
                    <TableHead className="min-w-[100px]">Demand Level</TableHead>
                    <TableHead className="min-w-[120px]">Performance</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data.routes.length > 0 ? (
                    data.routes.map((route) => (
                      <TableRow key={route.routeCode}>
                        <TableCell className="font-medium">{route.routeCode}</TableCell>
                        <TableCell>{route.busName}</TableCell>
                        <TableCell className="font-semibold">{route.totalBookings}</TableCell>
                        <TableCell>{route.capacity}</TableCell>
                        <TableCell>{route.utilizationPercentage.toFixed(1)}%</TableCell>
                        <TableCell>{route.availableSeats}</TableCell>
                        <TableCell>
                          <Badge className={getDemandBadgeColor(route.demandLevel)}>
                            {route.demandLevel}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="w-full">
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-green-600 h-2 rounded-full"
                                style={{ width: `${route.utilizationPercentage}%` }}
                              ></div>
                            </div>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                        No route demand data found for the selected date range
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>

          {/* Mobile Card View */}
          <div className="lg:hidden space-y-4">
            {data.routes.length > 0 ? (
              data.routes.map((route) => (
                <Card key={route.routeCode} className="border border-gray-200">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h4 className="font-semibold text-sm sm:text-base">{route.busName}</h4>
                        <p className="text-xs sm:text-sm text-gray-500">Route: {route.routeCode}</p>
                      </div>
                      <Badge className={getDemandBadgeColor(route.demandLevel)}>
                        {route.demandLevel}
                      </Badge>
                    </div>

                    <div className="grid grid-cols-2 gap-2 text-xs sm:text-sm mb-3">
                      <div>
                        <span className="text-gray-500">Bookings:</span>
                        <span className="ml-2 font-semibold">{route.totalBookings}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Capacity:</span>
                        <span className="ml-2 font-medium">{route.capacity}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Utilization:</span>
                        <span className="ml-2 font-medium">{route.utilizationPercentage.toFixed(1)}%</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Available:</span>
                        <span className="ml-2 font-medium">{route.availableSeats}</span>
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between text-xs text-gray-500 mb-1">
                        <span>Performance</span>
                        <span>{route.utilizationPercentage.toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-green-600 h-2 rounded-full"
                          style={{ width: `${route.utilizationPercentage}%` }}
                        ></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                No route demand data found for the selected date range
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Daily Trends */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg sm:text-xl">Daily Booking Trends</CardTitle>
        </CardHeader>
        <CardContent>
          {data.dailyTrends.length > 0 ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {data.dailyTrends.map((day) => (
                  <div key={day.date} className="p-3 sm:p-4 border rounded-lg hover:shadow-sm transition-shadow">
                    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 mb-2">
                      <span className="text-xs sm:text-sm font-medium text-gray-600">
                        {new Date(day.date).toLocaleDateString()}
                      </span>
                      <span className="text-base sm:text-lg font-bold text-blue-600">
                        {day.totalBookings} bookings
                      </span>
                    </div>
                    <div className="text-xs text-gray-500">
                      {Object.keys(day.routeBreakdown).length} routes active
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No daily trend data available for the selected date range
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
