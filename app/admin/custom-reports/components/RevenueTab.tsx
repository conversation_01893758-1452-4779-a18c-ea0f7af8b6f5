'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
// import { Progress } from '@/components/ui/progress';
import { Download, TrendingUp, DollarSign, BarChart3 } from 'lucide-react';
import { toast } from 'sonner';

interface DateRange {
  startDate: string;
  endDate: string;
}

interface RouteRevenue {
  busRoute: string;
  busName: string;
  totalRevenue: number;
  bookingCount: number;
  revenuePerBooking: number;
  percentageOfTotal: number;
}

interface DailyRevenue {
  date: string;
  revenue: number;
}

interface RevenueData {
  totalRevenue: number;
  totalBookings: number;
  averageRevenuePerBooking: number;
  routes: RouteRevenue[];
  dailyBreakdown: DailyRevenue[];
  summary: {
    dateRange: {
      startDate: string;
      endDate: string;
    };
    totalRoutes: number;
    highestRevenueRoute: RouteRevenue | null;
    lowestRevenueRoute: RouteRevenue | null;
  };
}

interface RevenueTabProps {
  dateRange: DateRange;
  isRefreshing: boolean;
}

export default function RevenueTab({ dateRange, isRefreshing }: RevenueTabProps) {
  const [data, setData] = useState<RevenueData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchRevenueData = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        start_date: dateRange.startDate,
        end_date: dateRange.endDate
      });

      const response = await fetch(`/api/admin/custom-reports/revenue?${params}`, {
        credentials: 'include'
      });

      const result = await response.json();

      if (result.success) {
        setData(result.data);
      } else {
        setError(result.error || 'Failed to fetch revenue data');
        toast.error('Failed to fetch revenue data');
      }
    } catch (error) {
      setError('Network error while fetching revenue data');
      toast.error('Network error while fetching revenue data');
      console.error('Revenue fetch error:', error);
    } finally {
      setIsLoading(false);
    }
  }, [dateRange]);

  useEffect(() => {
    fetchRevenueData();
  }, [fetchRevenueData]);

  const handleExportRevenue = () => {
    if (!data) return;
    
    const headers = ['Bus Route', 'Bus Name', 'Total Revenue', 'Booking Count', 'Revenue Per Booking', 'Percentage of Total'];
    const csvContent = [
      headers.join(','),
      ...data.routes.map(route => [
        route.busRoute,
        `"${route.busName}"`,
        route.totalRevenue,
        route.bookingCount,
        route.revenuePerBooking.toFixed(2),
        route.percentageOfTotal.toFixed(2) + '%'
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `revenue-report-${dateRange.startDate}-to-${dateRange.endDate}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    
    toast.success('Revenue data exported successfully');
  };

  const handleExportDaily = () => {
    if (!data) return;
    
    const headers = ['Date', 'Revenue'];
    const csvContent = [
      headers.join(','),
      ...data.dailyBreakdown.map(day => [
        day.date,
        day.revenue
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `daily-revenue-${dateRange.startDate}-to-${dateRange.endDate}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    
    toast.success('Daily revenue data exported successfully');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading revenue data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={fetchRevenueData} variant="outline">
          Try Again
        </Button>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">No revenue data available</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 sm:p-6">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-xs sm:text-sm font-medium text-gray-600">Total Revenue</p>
                <p className="text-xl sm:text-2xl font-bold text-green-600">₹{data.totalRevenue.toLocaleString()}</p>
              </div>
              <DollarSign className="h-6 w-6 sm:h-8 sm:w-8 text-green-600 ml-auto flex-shrink-0" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 sm:p-6">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-xs sm:text-sm font-medium text-gray-600">Total Bookings</p>
                <p className="text-xl sm:text-2xl font-bold text-blue-600">{data.totalBookings}</p>
              </div>
              <BarChart3 className="h-6 w-6 sm:h-8 sm:w-8 text-blue-600 ml-auto flex-shrink-0" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 sm:p-6">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-xs sm:text-sm font-medium text-gray-600">Avg Revenue/Booking</p>
                <p className="text-xl sm:text-2xl font-bold text-purple-600">₹{data.averageRevenuePerBooking.toFixed(2)}</p>
              </div>
              <TrendingUp className="h-6 w-6 sm:h-8 sm:w-8 text-purple-600 ml-auto flex-shrink-0" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 sm:p-6">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-xs sm:text-sm font-medium text-gray-600">Active Routes</p>
                <p className="text-xl sm:text-2xl font-bold text-orange-600">{data.summary.totalRoutes}</p>
              </div>
              <BarChart3 className="h-6 w-6 sm:h-8 sm:w-8 text-orange-600 ml-auto flex-shrink-0" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Performing Routes */}
      {data.summary.highestRevenueRoute && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base sm:text-lg text-green-600">Highest Revenue Route</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p className="text-sm sm:text-base font-semibold">{data.summary.highestRevenueRoute.busName}</p>
                <p className="text-xs sm:text-sm text-gray-600">Route: {data.summary.highestRevenueRoute.busRoute}</p>
                <p className="text-base sm:text-lg font-bold text-green-600">₹{data.summary.highestRevenueRoute.totalRevenue.toLocaleString()}</p>
                <p className="text-xs sm:text-sm text-gray-600">{data.summary.highestRevenueRoute.bookingCount} bookings</p>
              </div>
            </CardContent>
          </Card>
          {data.summary.lowestRevenueRoute && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base sm:text-lg text-orange-600">Lowest Revenue Route</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p className="text-sm sm:text-base font-semibold">{data.summary.lowestRevenueRoute.busName}</p>
                  <p className="text-xs sm:text-sm text-gray-600">Route: {data.summary.lowestRevenueRoute.busRoute}</p>
                  <p className="text-base sm:text-lg font-bold text-orange-600">₹{data.summary.lowestRevenueRoute.totalRevenue.toLocaleString()}</p>
                  <p className="text-xs sm:text-sm text-gray-600">{data.summary.lowestRevenueRoute.bookingCount} bookings</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Route-wise Revenue Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
            <span className="text-lg sm:text-xl">Route-wise Revenue Breakdown</span>
            <Button onClick={handleExportRevenue} variant="outline" size="sm" className="h-10 self-start sm:self-auto">
              <Download className="w-4 h-4 mr-2" />
              Export Routes
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Desktop Table View */}
          <div className="hidden lg:block">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="min-w-[100px]">Route</TableHead>
                    <TableHead className="min-w-[150px]">Bus Name</TableHead>
                    <TableHead className="min-w-[100px]">Revenue</TableHead>
                    <TableHead className="min-w-[80px]">Bookings</TableHead>
                    <TableHead className="min-w-[100px]">Avg/Booking</TableHead>
                    <TableHead className="min-w-[80px]">% of Total</TableHead>
                    <TableHead className="min-w-[120px]">Performance</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data.routes.length > 0 ? (
                    data.routes.map((route, index) => (
                      <TableRow key={route.busRoute}>
                        <TableCell className="font-medium">{route.busRoute}</TableCell>
                        <TableCell>{route.busName}</TableCell>
                        <TableCell className="font-semibold text-green-600">₹{route.totalRevenue.toLocaleString()}</TableCell>
                        <TableCell>{route.bookingCount}</TableCell>
                        <TableCell>₹{route.revenuePerBooking.toFixed(2)}</TableCell>
                        <TableCell>{route.percentageOfTotal.toFixed(1)}%</TableCell>
                        <TableCell>
                          <div className="w-full">
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-blue-600 h-2 rounded-full"
                                style={{ width: `${route.percentageOfTotal}%` }}
                              ></div>
                            </div>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                        No revenue data found for the selected date range
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>

          {/* Mobile Card View */}
          <div className="lg:hidden space-y-4">
            {data.routes.length > 0 ? (
              data.routes.map((route) => (
                <Card key={route.busRoute} className="border border-gray-200">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h4 className="font-semibold text-sm sm:text-base">{route.busName}</h4>
                        <p className="text-xs sm:text-sm text-gray-500">Route: {route.busRoute}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-green-600 text-sm sm:text-base">₹{route.totalRevenue.toLocaleString()}</p>
                        <p className="text-xs text-gray-500">{route.percentageOfTotal.toFixed(1)}% of total</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-2 text-xs sm:text-sm mb-3">
                      <div>
                        <span className="text-gray-500">Bookings:</span>
                        <span className="ml-2 font-medium">{route.bookingCount}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Avg/Booking:</span>
                        <span className="ml-2 font-medium">₹{route.revenuePerBooking.toFixed(2)}</span>
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between text-xs text-gray-500 mb-1">
                        <span>Performance</span>
                        <span>{route.percentageOfTotal.toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${route.percentageOfTotal}%` }}
                        ></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                No revenue data found for the selected date range
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Daily Revenue Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
            <span className="text-lg sm:text-xl">Daily Revenue Breakdown</span>
            <Button onClick={handleExportDaily} variant="outline" size="sm" className="h-10 self-start sm:self-auto">
              <Download className="w-4 h-4 mr-2" />
              Export Daily
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {data.dailyBreakdown.length > 0 ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {data.dailyBreakdown.map((day) => (
                  <div key={day.date} className="p-3 sm:p-4 border rounded-lg hover:shadow-sm transition-shadow">
                    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1">
                      <span className="text-xs sm:text-sm font-medium text-gray-600">
                        {new Date(day.date).toLocaleDateString()}
                      </span>
                      <span className="text-base sm:text-lg font-bold text-green-600">
                        ₹{day.revenue.toLocaleString()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No daily revenue data available for the selected date range
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
