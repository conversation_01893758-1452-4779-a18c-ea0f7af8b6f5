'use client';

import { useState, useEffect, useCallback } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { PageTransition } from '@/components/ui/page-transition';
import { useAdmin, withAdminAuth } from '@/contexts/AdminContext';
import { Bus, Plus, Edit, Trash2, Save, X, ArrowLeft, DownloadCloud, Search, Filter, MoreVertical } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'sonner';
import Link from 'next/link';

interface BusData {
  id?: number;
  name: string;
  route_code: string;
  available_seats?: number;
  total_seats?: number;
  is_active: boolean;
}

function BusManagement() {
  const { user } = useAdmin();
  const [buses, setBuses] = useState<BusData[]>([]);
  const [filteredBuses, setFilteredBuses] = useState<BusData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [editingBus, setEditingBus] = useState<BusData | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [formData, setFormData] = useState<BusData>({
    name: '',
    route_code: '',
    available_seats: 10,
    total_seats: 10,
    is_active: true
  });

  useEffect(() => {
    fetchBuses();
  }, []);

  const filterBuses = useCallback(() => {
    let filtered = buses;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(bus =>
        bus.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        bus.route_code.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(bus => 
        statusFilter === 'active' ? bus.is_active : !bus.is_active
      );
    }

    setFilteredBuses(filtered);
  }, [buses, searchTerm, statusFilter]);

  useEffect(() => {
    filterBuses();
  }, [filterBuses]);

  const fetchBuses = async () => {
    try {
      const response = await fetch('/api/admin/buses');
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setBuses(result.data);
        }
      }
    } catch (error) {
      toast.error('Failed to fetch buses');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreate = async () => {
    // Validate form data
    if (formData.available_seats && formData.total_seats && formData.available_seats > formData.total_seats) {
      toast.error('Available seats cannot exceed total seats');
      return;
    }

    try {
      const response = await fetch('/api/admin/buses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
        credentials: 'include'
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success('Bus created successfully');
        setBuses([...buses, result.data]);
        setIsCreateModalOpen(false);
        setFormData({ name: '', route_code: '', available_seats: 10, total_seats: 10, is_active: true });
      } else {
        toast.error(result.error || 'Failed to create bus');
      }
    } catch (error) {
      toast.error('Failed to create bus');
    }
  };

  const handleUpdate = async () => {
    if (!editingBus) return;

    // Validate form data
    if (editingBus.available_seats && editingBus.total_seats && editingBus.available_seats > editingBus.total_seats) {
      toast.error('Available seats cannot exceed total seats');
      return;
    }

    try {
      const response = await fetch('/api/admin/buses', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editingBus),
        credentials: 'include'
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success('Bus updated successfully');
        setBuses(buses.map(bus => bus.id === editingBus.id ? result.data : bus));
        setIsEditModalOpen(false);
        setEditingBus(null);
      } else {
        toast.error(result.error || 'Failed to update bus');
      }
    } catch (error) {
      toast.error('Failed to update bus');
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this bus? This will also delete all associated route stops.')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/buses?id=${id}`, {
        method: 'DELETE',
        credentials: 'include'
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success('Bus deleted successfully');
        setBuses(buses.filter(bus => bus.id !== id));
      } else {
        toast.error(result.error || 'Failed to delete bus');
      }
    } catch (error) {
      toast.error('Failed to delete bus');
    }
  };

  const handleExport = () => {
    if (!filteredBuses || filteredBuses.length === 0) {
      toast.error('No buses to export');
      return;
    }

    const headers = ['id', 'name', 'route_code', 'total_seats', 'available_seats', 'is_active'];
    const rows = filteredBuses.map((b) => [
      b.id ?? '',
      b.name ?? '',
      b.route_code ?? '',
      b.total_seats ?? '',
      b.available_seats ?? '',
      b.is_active ? 'true' : 'false'
    ]);

    const escapeCell = (cell: any) => {
      const str = String(cell ?? '');
      return `"${str.replace(/"/g, '""')}"`;
    };

    const csv = [headers, ...rows].map((r) => r.map(escapeCell).join(',')).join('\n');

    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `buses_${new Date().toISOString().slice(0,10)}.csv`;
    document.body.appendChild(a);
    a.click();
    a.remove();
    URL.revokeObjectURL(url);

    toast.success('CSV exported successfully');
  };

  const openEditModal = (bus: BusData) => {
    setEditingBus(bus);
    setIsEditModalOpen(true);
  };

  const resetForm = () => {
    setFormData({ name: '', route_code: '', available_seats: 10, total_seats: 10, is_active: true });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading buses...</p>
        </div>
      </div>
    );
  }

  return (
    <PageTransition>
      <div className="min-h-screen p-6 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="mb-8"
          >
            <div className="flex flex-col gap-4">
              {/* Back Button - Now at the top in mobile view */}
              <div className="w-full">
                <Link href="/admin/dashboard">
                  <Button variant="outline" size="sm" className="w-full sm:w-auto">
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Dashboard
                  </Button>
                </Link>
              </div>
              
              {/* Header content with flex for desktop layout */}
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                  <h1 className="text-3xl font-bold text-gray-800 mb-2">Bus Management</h1>
                  <p className="text-gray-600">Manage your bus fleet and routes</p>
                </div>

                <div className="flex items-center gap-3 w-full sm:w-auto">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleExport}
                    className="border-gray-300 hover:bg-gray-50 flex-1 sm:flex-none"
                  >
                    <DownloadCloud className="w-4 h-4 mr-2" />
                    Export CSV
                  </Button>

                  <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
                    <DialogTrigger asChild>
                      <Button 
                        onClick={() => {
                          resetForm();
                          setIsCreateModalOpen(true);
                        }}
                        className="bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 flex-1 sm:flex-none"
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        Add New Bus
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl">
                      <DialogHeader>
                        <DialogTitle>Create New Bus</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-6 py-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="name">Bus Name</Label>
                            <Input
                              id="name"
                              value={formData.name}
                              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                              placeholder="Enter bus name (e.g., Thodupuzha Express)"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="route_code">Route Code</Label>
                            <Input
                              id="route_code"
                              value={formData.route_code}
                              onChange={(e) => setFormData({ ...formData, route_code: e.target.value.startsWith('route-') ? e.target.value : `route-${e.target.value}` })}
                              placeholder="Enter route number (e.g., 1)"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="total_seats">Total Seats</Label>
                            <Input
                              id="total_seats"
                              type="number"
                              min="1"
                              max="200"
                              value={formData.total_seats}
                              onChange={(e) => {
                                const totalSeats = parseInt(e.target.value) || 0;
                                setFormData({
                                  ...formData,
                                  total_seats: totalSeats,
                                  available_seats: Math.min(
                                    Math.max(formData.available_seats ?? 0, 0),
                                    totalSeats
                                  ),
                                });
                              }}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="available_seats">Available Seats</Label>
                            <Input
                              id="available_seats"
                              type="number"
                              min="0"
                              max={formData.total_seats || 200}
                              value={formData.available_seats}
                              onChange={(e) => {
                                const availableSeats = parseInt(e.target.value) || 0;
                                const maxSeats = formData.total_seats || 0;
                                setFormData({
                                  ...formData,
                                  available_seats: Math.min(
                                    Math.max(availableSeats, 0),
                                    maxSeats
                                  ),
                                });
                              }}
                            />
                            <p className="text-xs text-gray-500">
                              Available seats cannot exceed total seats ({formData.total_seats || 0})
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Switch
                            id="is_active"
                            checked={formData.is_active}
                            onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
                          />
                          <Label htmlFor="is_active">Active</Label>
                        </div>
                        <div className="flex gap-3 pt-4">
                          <Button onClick={handleCreate} className="flex-1">
                            <Save className="w-4 h-4 mr-2" />
                            Create Bus
                          </Button>
                          <Button variant="outline" onClick={() => setIsCreateModalOpen(false)} className="flex-1">
                            <X className="w-4 h-4 mr-2" />
                            Cancel
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Filters and Search */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-6"
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex flex-col sm:flex-row gap-4 items-center">
                  <div className="relative flex-1 max-w-md">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder="Search buses by name or route code..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <Filter className="w-4 h-4 text-gray-400" />
                    <select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value as 'all' | 'active' | 'inactive')}
                      className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-red-500"
                    >
                      <option value="all">All Buses</option>
                      <option value="active">Active Only</option>
                      <option value="inactive">Inactive Only</option>
                    </select>
                  </div>
                  <div className="text-sm text-gray-600">
                    Showing {filteredBuses.length} of {buses.length} buses
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Buses Grid */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          >
            <AnimatePresence>
              {filteredBuses.map((bus, index) => (
                <motion.div
                  key={bus.id}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                >
                  <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-red-500">
                    <CardHeader className="pb-3">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <CardTitle className="text-lg font-semibold text-gray-800 mb-1 line-clamp-2">
                            {bus.name}
                          </CardTitle>
                          <p className="text-sm text-gray-600 font-mono">{bus.route_code}</p>
                        </div>
                        <Badge 
                          variant={bus.is_active ? "default" : "secondary"}
                          className={`${bus.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}
                        >
                          {bus.is_active ? "Active" : "Inactive"}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="space-y-3 mb-4">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Total Seats</span>
                          <Badge variant="outline" className="font-semibold bg-blue-50">
                            {bus.total_seats ?? 50}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Available</span>
                          <Badge variant="outline" className="font-semibold bg-green-50">
                            {bus.available_seats ?? 0}
                          </Badge>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ 
                              width: `${((bus.total_seats ?? 0) - (bus.available_seats ?? 0)) / (bus.total_seats ?? 1) * 100}%` 
                            }}
                          ></div>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => openEditModal(bus)}
                          className="flex-1"
                        >
                          <Edit className="w-4 h-4 mr-1" />
                          Edit
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-red-600 border-red-600 hover:bg-red-50 flex-1"
                          onClick={() => handleDelete(bus.id!)}
                        >
                          <Trash2 className="w-4 h-4 mr-1" />
                          Delete
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          </motion.div>

          {/* Empty State */}
          {filteredBuses.length === 0 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              className="text-center py-16"
            >
              <Bus className="w-20 h-20 mx-auto text-gray-300 mb-6" />
              <h3 className="text-xl font-semibold text-gray-600 mb-2">
                {searchTerm || statusFilter !== 'all' ? 'No buses found' : 'No buses yet'}
              </h3>
              <p className="text-gray-500 mb-6 max-w-md mx-auto">
                {searchTerm || statusFilter !== 'all' 
                  ? 'Try adjusting your search or filters to find what you\'re looking for.'
                  : 'Get started by adding your first bus to the fleet.'
                }
              </p>
              {!searchTerm && statusFilter === 'all' && (
                <Button 
                  onClick={() => setIsCreateModalOpen(true)}
                  className="bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add New Bus
                </Button>
              )}
            </motion.div>
          )}

          {/* Edit Modal */}
          <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Edit Bus</DialogTitle>
              </DialogHeader>
              {editingBus && (
                <div className="space-y-6 py-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="edit-name">Bus Name</Label>
                      <Input
                        id="edit-name"
                        value={editingBus.name}
                        onChange={(e) => setEditingBus({ ...editingBus, name: e.target.value })}
                        placeholder="Enter bus name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="edit-route_code">Route Code</Label>
                      <Input
                        id="edit-route_code"
                        value={editingBus.route_code}
                        disabled
                        className="bg-gray-100"
                        title="Route code cannot be changed"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="edit-total_seats">Total Seats</Label>
                      <Input
                        id="edit-total_seats"
                        type="number"
                        min="1"
                        max="200"
                        value={editingBus.total_seats}
                        onChange={(e) => {
                          const totalSeats = parseInt(e.target.value) || 0;
                          setEditingBus({
                            ...editingBus,
                            total_seats: totalSeats,
                            available_seats: Math.min(
                              Math.max(editingBus.available_seats ?? 0, 0),
                              totalSeats
                            ),
                          });
                        }}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="edit-available_seats">Available Seats</Label>
                      <Input
                        id="edit-available_seats"
                        type="number"
                        min="0"
                        max={editingBus.total_seats || 200}
                        value={editingBus.available_seats}
                        onChange={(e) => {
                          const availableSeats = parseInt(e.target.value) || 0;
                          const maxSeats = editingBus.total_seats || 0;
                          setEditingBus({
                            ...editingBus,
                            available_seats: Math.min(
                              Math.max(availableSeats, 0),
                              maxSeats
                            ),
                          });
                        }}
                      />
                      <p className="text-xs text-gray-500">
                        Available seats cannot exceed total seats ({editingBus.total_seats || 0})
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="edit-is_active"
                      checked={editingBus.is_active}
                      onCheckedChange={(checked) => setEditingBus({ ...editingBus, is_active: checked })}
                    />
                    <Label htmlFor="edit-is_active">Active</Label>
                  </div>
                  <div className="flex gap-3 pt-4">
                    <Button onClick={handleUpdate} className="flex-1">
                      <Save className="w-4 h-4 mr-2" />
                      Update Bus
                    </Button>
                    <Button variant="outline" onClick={() => setIsEditModalOpen(false)} className="flex-1">
                      <X className="w-4 h-4 mr-2" />
                      Cancel
                    </Button>
                  </div>
                </div>
              )}
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </PageTransition>
  );
}

export default withAdminAuth(BusManagement);
