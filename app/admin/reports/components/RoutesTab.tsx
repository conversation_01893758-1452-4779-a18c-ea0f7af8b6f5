'use client';

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ChartContainer } from '@/components/ui/chart';
import { Bar, BarChart, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { Bus, Users, ArrowUpDown, ArrowUp, ArrowDown, TrendingUp } from 'lucide-react';
import type { RoutesTabProps, SortConfig } from '@/types/reports';

const RoutesTab: React.FC<RoutesTabProps> = ({ data, isLoading, error, onRefresh }) => {
  const [sortConfig, setSortConfig] = useState<SortConfig | null>({
    key: 'totalBookings',
    direction: 'desc'
  });

  // Get demand badge variant
  const getDemandBadgeVariant = (demandLevel: string) => {
    switch (demandLevel) {
      case 'High': return 'destructive';
      case 'Medium': return 'default';
      case 'Low': return 'secondary';
      default: return 'secondary';
    }
  };

  // Sort data
  const sortedData = useMemo(() => {
    if (!data?.routes || !sortConfig) return data?.routes || [];

    return [...data.routes].sort((a, b) => {
      const aValue = a[sortConfig.key as keyof typeof a];
      const bValue = b[sortConfig.key as keyof typeof b];

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortConfig.direction === 'asc' ? aValue - bValue : bValue - aValue;
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortConfig.direction === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      return 0;
    });
  }, [data?.routes, sortConfig]);

  // Handle sort
  const handleSort = (key: string) => {
    setSortConfig(prev => ({
      key,
      direction: prev?.key === key && prev.direction === 'desc' ? 'asc' : 'desc'
    }));
  };

  // Get sort icon
  const getSortIcon = (key: string) => {
    if (sortConfig?.key !== key) return <ArrowUpDown className="w-4 h-4" />;
    return sortConfig.direction === 'asc'
      ? <ArrowUp className="w-4 h-4" />
      : <ArrowDown className="w-4 h-4" />;
  };

  // Error state
  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={onRefresh} variant="outline">
          Try Again
        </Button>
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
        {Array.from({ length: 3 }).map((_, i) => (
          <Skeleton key={i} className="h-24 sm:h-32 w-full" />
        ))}
      </div>
    );
  }

  // Empty state
  if (!data?.routes || data.routes.length === 0) {
    return (
      <div className="text-center py-6 sm:py-8">
        <Bus className="w-12 h-12 sm:w-16 sm:h-16 text-gray-400 mx-auto mb-3 sm:mb-4" />
        <p className="text-gray-500 text-base sm:text-lg">No route data available</p>
        <Button onClick={onRefresh} variant="outline" className="mt-3 sm:mt-4 text-xs sm:text-sm" size="sm">
          Refresh Data
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Summary Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
        <Card>
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center gap-2 sm:gap-3">
              <Bus className="w-6 h-6 sm:w-8 sm:h-8 text-blue-600" />
              <div>
                <p className="text-xs sm:text-sm text-gray-600">Total Routes</p>
                <p className="text-xl sm:text-2xl font-bold">{data?.routes.length || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center gap-2 sm:gap-3">
              <Users className="w-6 h-6 sm:w-8 sm:h-8 text-green-600" />
              <div>
                <p className="text-xs sm:text-sm text-gray-600">Total Bookings</p>
                <p className="text-xl sm:text-2xl font-bold">
                  {data?.totalBookings || 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Routes Table */}
      <Card>
        <CardHeader className="py-3 sm:py-6">
          <CardTitle className="flex items-center gap-2 text-base sm:text-xl">
            <TrendingUp className="w-4 h-4 sm:w-5 sm:h-5" />
            Booking Demand by Route
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0 sm:p-6 pt-0">
          {data?.routes.length === 0 ? (
            <div className="text-center py-6 sm:py-8 text-gray-500 text-sm sm:text-base">
              No route data available
            </div>
          ) : (
            <div className="overflow-x-auto -mx-2 sm:mx-0">
              <Table className="w-full border-collapse">
                <TableHeader>
                  <TableRow>
                    <TableHead
                      className="cursor-pointer hover:bg-gray-50 text-xs sm:text-sm w-[18%]"
                      onClick={() => handleSort('routeCode')}
                    >
                      <div className="flex items-center gap-1 sm:gap-2">
                        <span className="whitespace-nowrap">Route</span>
                        {getSortIcon('routeCode')}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-gray-50 text-xs sm:text-sm w-[30%]"
                      onClick={() => handleSort('busName')}
                    >
                      <div className="flex items-center gap-1 sm:gap-2">
                        <span className="whitespace-nowrap">Bus Name</span>
                        {getSortIcon('busName')}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-gray-50 text-xs sm:text-sm w-[17%]"
                      onClick={() => handleSort('totalBookings')}
                    >
                      <div className="flex items-center gap-1 sm:gap-2">
                        <span className="whitespace-nowrap">Bookings</span>
                        {getSortIcon('totalBookings')}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-gray-50 text-xs sm:text-sm w-[15%]"
                      onClick={() => handleSort('bookingPercentage')}
                    >
                      <div className="flex items-center gap-1 sm:gap-2">
                        <span className="whitespace-nowrap">%</span>
                        {getSortIcon('bookingPercentage')}
                      </div>
                    </TableHead>
                    <TableHead className="text-xs sm:text-sm w-[20%]">Demand</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedData.map((route) => (
                    <TableRow key={route.routeCode}>
                      <TableCell className="font-medium text-xs sm:text-sm py-2 sm:py-4">{route.routeCode}</TableCell>
                      <TableCell className="text-xs sm:text-sm py-2 sm:py-4 truncate max-w-[120px] sm:max-w-none">{route.busName}</TableCell>
                      <TableCell className="text-center py-2 sm:py-4">
                        <span className="inline-flex items-center px-1.5 sm:px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {route.totalBookings}
                        </span>
                      </TableCell>
                      <TableCell className="text-center text-xs sm:text-sm py-2 sm:py-4">
                        <span className="font-semibold">{route.bookingPercentage}%</span>
                      </TableCell>
                      <TableCell className="py-2 sm:py-4">
                        <Badge variant={getDemandBadgeVariant(route.demandLevel)} className="text-xs whitespace-nowrap">
                          {route.demandLevel}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Demand Level Legend */}
      <Card>
        <CardHeader className="py-3 sm:py-6">
          <CardTitle className="text-base sm:text-lg">Demand Level Classification</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-4 text-xs sm:text-sm">
            <div className="flex items-center gap-2 sm:gap-3">
              <div className="w-3 h-3 sm:w-4 sm:h-4 bg-green-500 rounded"></div>
              <span>Low: &lt;5% of total bookings</span>
            </div>
            <div className="flex items-center gap-2 sm:gap-3">
              <div className="w-3 h-3 sm:w-4 sm:h-4 bg-yellow-500 rounded"></div>
              <span>Medium: 5-14% of total bookings</span>
            </div>
            <div className="flex items-center gap-2 sm:gap-3">
              <div className="w-3 h-3 sm:w-4 sm:h-4 bg-red-500 rounded"></div>
              <span>High: ≥15% of total bookings</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RoutesTab;
