'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { withAdminAuth } from '@/contexts/AdminContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { RefreshCw, BarChart3, Home } from 'lucide-react';
import { toast } from 'sonner';
import { motion } from 'framer-motion';
import type {
  RevenueApiResponse,
  RoutesApiResponse,
  StopsApiResponse,
  RoundsApiResponse,
  AnalyticsDashboardState
} from '@/types/reports';

// Import tab components (will create these next)
import RevenueTab from './components/RevenueTab';
import RoutesTab from './components/RoutesTab';
import StopsTab from './components/StopsTab';
import HistoryTab from './components/HistoryTab';

function ReportsPage() {
  const [state, setState] = useState<AnalyticsDashboardState>({
    activeTab: 'revenue',
    selectedRoute: '',
    refreshInfo: {
      lastUpdated: new Date(),
      isRefreshing: false
    },
    sortConfig: null
  });

  // Data states
  const [revenueData, setRevenueData] = useState<RevenueApiResponse['data'] | null>(null);
  const [routesData, setRoutesData] = useState<RoutesApiResponse['data'] | null>(null);
  const [stopsData, setStopsData] = useState<StopsApiResponse['data'] | null>(null);
  const [roundsData, setRoundsData] = useState<RoundsApiResponse['data'] | null>(null);

  // Loading states
  const [isLoadingRevenue, setIsLoadingRevenue] = useState(false);
  const [isLoadingRoutes, setIsLoadingRoutes] = useState(false);
  const [isLoadingStops, setIsLoadingStops] = useState(false);
  const [isLoadingRounds, setIsLoadingRounds] = useState(false);

  // Error states
  const [revenueError, setRevenueError] = useState<string | null>(null);
  const [routesError, setRoutesError] = useState<string | null>(null);
  const [stopsError, setStopsError] = useState<string | null>(null);
  const [roundsError, setRoundsError] = useState<string | null>(null);

  // Available routes for stops dropdown
  const [availableRoutes, setAvailableRoutes] = useState<{ value: string; label: string }[]>([]);

  // Fetch functions
  const fetchRevenueData = useCallback(async () => {
    setIsLoadingRevenue(true);
    setRevenueError(null);
    try {
      const response = await fetch('/api/admin/reports/revenue', {
        credentials: 'include'
      });
      const result: RevenueApiResponse = await response.json();
      
      if (result.success) {
        setRevenueData(result.data);
      } else {
        setRevenueError(result.error || 'Failed to fetch revenue data');
      }
    } catch (error) {
      setRevenueError('Network error while fetching revenue data');
      console.error('Revenue fetch error:', error);
    } finally {
      setIsLoadingRevenue(false);
    }
  }, []);

  const fetchRoutesData = useCallback(async () => {
    setIsLoadingRoutes(true);
    setRoutesError(null);
    try {
      const response = await fetch('/api/admin/reports/routes', {
        credentials: 'include'
      });
      const result: RoutesApiResponse = await response.json();
      
      if (result.success) {
        setRoutesData(result.data);
        // Update available routes for stops dropdown
        const routes = result.data.routes.map(route => ({
          value: route.routeCode,
          label: route.busName
        }));
        setAvailableRoutes(routes);
      } else {
        setRoutesError(result.error || 'Failed to fetch routes data');
      }
    } catch (error) {
      setRoutesError('Network error while fetching routes data');
      console.error('Routes fetch error:', error);
    } finally {
      setIsLoadingRoutes(false);
    }
  }, []);

  const fetchStopsData = useCallback(async (busRoute: string) => {
    if (!busRoute) {
      setStopsData(null);
      return;
    }

    setIsLoadingStops(true);
    setStopsError(null);
    try {
      const response = await fetch(`/api/admin/reports/stops?bus_route=${encodeURIComponent(busRoute)}`, {
        credentials: 'include'
      });
      const result: StopsApiResponse = await response.json();
      
      if (result.success) {
        setStopsData(result.data);
      } else {
        setStopsError(result.error || 'Failed to fetch stops data');
      }
    } catch (error) {
      setStopsError('Network error while fetching stops data');
      console.error('Stops fetch error:', error);
    } finally {
      setIsLoadingStops(false);
    }
  }, []);

  const fetchRoundsData = useCallback(async () => {
    setIsLoadingRounds(true);
    setRoundsError(null);
    try {
      const response = await fetch('/api/admin/reports/rounds', {
        credentials: 'include'
      });
      const result: RoundsApiResponse = await response.json();
      
      if (result.success) {
        setRoundsData(result.data);
      } else {
        setRoundsError(result.error || 'Failed to fetch rounds data');
      }
    } catch (error) {
      setRoundsError('Network error while fetching rounds data');
      console.error('Rounds fetch error:', error);
    } finally {
      setIsLoadingRounds(false);
    }
  }, []);

  // Refresh all data
  const refreshAllData = useCallback(async () => {
    setState(prev => ({
      ...prev,
      refreshInfo: { ...prev.refreshInfo, isRefreshing: true }
    }));

    try {
      await Promise.all([
        fetchRevenueData(),
        fetchRoutesData(),
        fetchRoundsData()
      ]);

      // Fetch stops data if a route is selected
      if (state.selectedRoute) {
        await fetchStopsData(state.selectedRoute);
      }

      setState(prev => ({
        ...prev,
        refreshInfo: {
          lastUpdated: new Date(),
          isRefreshing: false
        }
      }));

      toast.success('Data refreshed successfully');
    } catch (error) {
      setState(prev => ({
        ...prev,
        refreshInfo: { ...prev.refreshInfo, isRefreshing: false }
      }));
      toast.error('Failed to refresh data');
    }
  }, [fetchRevenueData, fetchRoutesData, fetchRoundsData, fetchStopsData, state.selectedRoute]);

  // Handle route selection for stops tab
  const handleRouteChange = useCallback((route: string) => {
    setState(prev => ({ ...prev, selectedRoute: route }));
    if (route) {
      fetchStopsData(route);
    } else {
      setStopsData(null);
    }
  }, [fetchStopsData]);

  // Handle tab change
  const handleTabChange = useCallback((tab: string) => {
    setState(prev => ({ ...prev, activeTab: tab as any }));
  }, []);

  // Initial data load
  useEffect(() => {
    refreshAllData();
  }, [refreshAllData]);

  // Auto-refresh every 5 minutes
  useEffect(() => {
    const interval = setInterval(refreshAllData, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, [refreshAllData]);

  return (
    <div className="min-h-screen p-2 sm:p-4 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 sm:mb-8 gap-4"
        >
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-1 sm:mb-2">Reports Dashboard</h1>
            <p className="text-sm sm:text-base text-gray-600">
              Last updated: {state.refreshInfo.lastUpdated.toLocaleTimeString()}
            </p>
          </div>
          <div className="flex flex-wrap items-center gap-2 sm:gap-4 w-full sm:w-auto">
            <Button
              onClick={() => window.location.href = '/admin/dashboard'} // Redirect to admin dashboard
              variant="ghost"
              className="flex items-center gap-2 text-sm sm:text-base w-full sm:w-auto justify-center"
              size="sm"
            >
              <Home className="w-4 h-4" />
              <span className="sm:inline">Back to Dashboard</span>
            </Button>
            <Button
              onClick={refreshAllData}
              disabled={state.refreshInfo.isRefreshing}
              variant="outline"
              className="flex items-center gap-2 text-sm sm:text-base w-full sm:w-auto justify-center"
              size="sm"
            >
              <RefreshCw className={`w-4 h-4 ${state.refreshInfo.isRefreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </motion.div>

        {/* Breadcrumb */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="mb-4 sm:mb-6"
        >
          <nav className="text-xs sm:text-sm text-gray-600 overflow-x-auto whitespace-nowrap pb-1">
            Admin &gt; Reports &gt; {state.activeTab.charAt(0).toUpperCase() + state.activeTab.slice(1)}
          </nav>
        </motion.div>

        {/* Main Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card className="shadow-sm border-0 sm:border sm:shadow-lg rounded-lg sm:rounded-xl overflow-hidden">
            <CardHeader className="px-3 py-4 sm:px-6 sm:py-6 bg-white border-b border-gray-100">
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 sm:gap-0">
                <CardTitle className="flex items-center gap-2 sm:gap-3 text-lg sm:text-xl lg:text-2xl font-bold text-gray-900">
                  <div className="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 bg-blue-100 rounded-lg">
                    <BarChart3 className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600" />
                  </div>
                  <span className="leading-tight">Analytics Dashboard</span>
                </CardTitle>
                <div className="text-xs sm:text-sm text-gray-500 font-medium">
                  Live Data • Auto-refresh enabled
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0 bg-gray-50 sm:bg-white">
              <Tabs value={state.activeTab} onValueChange={handleTabChange} className="w-full">
                {/* Mobile-optimized tabs with horizontal scroll on very small screens */}
                <div className="sticky top-0 z-10 bg-white border-b border-gray-200 px-3 sm:px-6 py-3 sm:py-4">
                  <TabsList className="w-full h-auto p-1 bg-gray-100 rounded-lg grid grid-cols-2 sm:grid-cols-4 gap-1">
                    <TabsTrigger 
                      value="revenue" 
                      className="flex-1 min-h-[44px] px-2 py-2 sm:px-4 sm:py-3 text-xs sm:text-sm font-medium rounded-md transition-all duration-200 data-[state=active]:bg-white data-[state=active]:text-blue-600 data-[state=active]:shadow-sm hover:bg-white/50"
                    >
                      <div className="flex flex-col items-center gap-1 sm:flex-row sm:gap-2">
                        <span className="text-[10px] sm:text-xs lg:text-sm font-semibold">Revenue</span>
                      </div>
                    </TabsTrigger>
                    <TabsTrigger 
                      value="routes" 
                      className="flex-1 min-h-[44px] px-2 py-2 sm:px-4 sm:py-3 text-xs sm:text-sm font-medium rounded-md transition-all duration-200 data-[state=active]:bg-white data-[state=active]:text-blue-600 data-[state=active]:shadow-sm hover:bg-white/50"
                    >
                      <div className="flex flex-col items-center gap-1 sm:flex-row sm:gap-2">
                        <span className="text-[10px] sm:text-xs lg:text-sm font-semibold">Routes</span>
                      </div>
                    </TabsTrigger>
                    <TabsTrigger 
                      value="stops" 
                      className="flex-1 min-h-[44px] px-2 py-2 sm:px-4 sm:py-3 text-xs sm:text-sm font-medium rounded-md transition-all duration-200 data-[state=active]:bg-white data-[state=active]:text-blue-600 data-[state=active]:shadow-sm hover:bg-white/50"
                    >
                      <div className="flex flex-col items-center gap-1 sm:flex-row sm:gap-2">
                        <span className="text-[10px] sm:text-xs lg:text-sm font-semibold">Stops</span>
                      </div>
                    </TabsTrigger>
                    <TabsTrigger 
                      value="history" 
                      className="flex-1 min-h-[44px] px-2 py-2 sm:px-4 sm:py-3 text-xs sm:text-sm font-medium rounded-md transition-all duration-200 data-[state=active]:bg-white data-[state=active]:text-blue-600 data-[state=active]:shadow-sm hover:bg-white/50"
                    >
                      <div className="flex flex-col items-center gap-1 sm:flex-row sm:gap-2">
                        <span className="text-[10px] sm:text-xs lg:text-sm font-semibold">History</span>
                      </div>
                    </TabsTrigger>
                  </TabsList>
                </div>

                {/* Tab Content with proper spacing and responsive layout */}
                <div className="px-3 py-4 sm:px-6 sm:py-6 bg-gray-50 sm:bg-white min-h-[400px]">
                  <TabsContent value="revenue" className="mt-0 space-y-4 sm:space-y-6">
                    <RevenueTab
                      data={revenueData}
                      isLoading={isLoadingRevenue}
                      error={revenueError}
                      onRefresh={fetchRevenueData}
                    />
                  </TabsContent>

                  <TabsContent value="routes" className="mt-0 space-y-4 sm:space-y-6">
                    <RoutesTab
                      data={routesData}
                      isLoading={isLoadingRoutes}
                      error={routesError}
                      onRefresh={fetchRoutesData}
                    />
                  </TabsContent>

                  <TabsContent value="stops" className="mt-0 space-y-4 sm:space-y-6">
                    <StopsTab
                      data={stopsData}
                      isLoading={isLoadingStops}
                      error={stopsError}
                      selectedRoute={state.selectedRoute}
                      onRouteChange={handleRouteChange}
                      onRefresh={() => fetchStopsData(state.selectedRoute)}
                      availableRoutes={availableRoutes}
                    />
                  </TabsContent>

                  <TabsContent value="history" className="mt-0 space-y-4 sm:space-y-6">
                    <HistoryTab
                      data={roundsData}
                      isLoading={isLoadingRounds}
                      error={roundsError}
                      onRefresh={fetchRoundsData}
                    />
                  </TabsContent>
                </div>
              </Tabs>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}

export default withAdminAuth(ReportsPage);
