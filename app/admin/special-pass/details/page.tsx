'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { PageTransition } from '@/components/ui/page-transition';
import { useSpecialPass } from '@/contexts/SpecialPassContext';
import { useAdmin, withAdminAuth } from '@/contexts/AdminContext';
import { motion } from 'framer-motion';
import { User, ArrowLeft, CheckCircle, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';

function SpecialPassDetails() {
  const { user } = useAdmin();
  const router = useRouter();
  const { specialPassData, updateSpecialPassData } = useSpecialPass();
  const [studentName, setStudentName] = useState(specialPassData.studentName || '');
  const [admissionNumber, setAdmissionNumber] = useState(specialPassData.admissionNumber || '');
  const [nameError, setNameError] = useState('');
  const [admissionError, setAdmissionError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Validation functions
  const validateStudentName = (name: string): boolean => {
    // Must contain 2-3 names (first name + surname, optionally middle name)
    const trimmedName = name.trim();
    if (!trimmedName) return false;
    
    const nameParts = trimmedName.split(/\s+/);
    return nameParts.length >= 2 && nameParts.length <= 3 && nameParts.every(part => part.length > 0);
  };

  const validateAdmissionNumber = (admissionNum: string): boolean => {
    // Pattern: 2 digits + 2-4 uppercase letters + 3 digits (e.g., "24CS094", "24MCA094", "24IMCA294")
    const regex = /^[0-9]{2}[A-Z]{2,4}[0-9]{3}$/;
    return regex.test(admissionNum);
  };

  // Handle input changes with validation
  const handleNameChange = (value: string) => {
    setStudentName(value);
    setNameError('');
    
    if (value.trim() && !validateStudentName(value)) {
      setNameError('Name must contain 2-3 words (first name + surname + optional middle name)');
    }
  };

  const handleAdmissionNumberChange = (value: string) => {
    // Convert to uppercase and remove invalid characters
    const sanitized = value.toUpperCase().replace(/[^A-Z0-9]/g, '');
    
    // Limit length to 9 characters
    const limited = sanitized.slice(0, 9);
    
    setAdmissionNumber(limited);
    setAdmissionError('');
    
    if (limited && !validateAdmissionNumber(limited)) {
      setAdmissionError('Format: 2 digits + 2-4 letters + 3 digits (e.g., 24CS094, 24MCA094)');
    }
  };

  // Check if we have required data from previous step
  useEffect(() => {
    if (!specialPassData.goDate || !specialPassData.returnDate) {
      toast.error('Please select travel dates first');
      router.push('/admin/special-pass');
      return;
    }
  }, [specialPassData, router]);

  const handleNext = () => {
    // Validate both fields
    if (!validateStudentName(studentName)) {
      setNameError('Name must contain 2-3 words (first name + surname + optional middle name)');
      toast.error('Please enter a valid student name');
      return;
    }

    if (!validateAdmissionNumber(admissionNumber)) {
      setAdmissionError('Format: 2 digits + 2-4 letters + 3 digits (e.g., 24CS094, 24MCA094)');
      toast.error('Please enter a valid admission number');
      return;
    }

    setIsLoading(true);
    
    // Update special pass context with student information
    updateSpecialPassData({
      studentName: studentName.trim(),
      admissionNumber: admissionNumber,
    });

    // Navigate to bus selection
    router.push('/admin/special-pass/buses');
  };

  const isFormValid = validateStudentName(studentName) && validateAdmissionNumber(admissionNumber);

  return (
    <PageTransition>
      <div className="min-h-screen p-3 sm:p-4 lg:p-6 bg-gradient-to-br from-orange-50 via-white to-red-50">
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4 sm:gap-6 mb-6 sm:mb-8"
          >
            <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
              <Button
                onClick={() => router.back()}
                variant="outline"
                size="sm"
                className="h-11 w-fit"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
              <div>
                <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-800 mb-1 sm:mb-2 flex items-center gap-2 sm:gap-3">
                  <User className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-orange-600" />
                  <span className="leading-tight">Student Details</span>
                </h1>
                <p className="text-sm sm:text-base text-gray-600">Enter student information for special booking</p>
              </div>
            </div>
          </motion.div>

          {/* Selected Dates Summary */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-6 sm:mb-8"
          >
            <Card className="border-green-200 bg-green-50">
              <CardContent className="p-3 sm:p-4">
                <div className="flex items-start sm:items-center gap-3">
                  <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 sm:mt-0 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-green-800 text-sm sm:text-base">Selected Travel Dates</h3>
                    <p className="text-xs sm:text-sm text-green-700 mt-1">
                      <span className="block sm:inline">Go: {new Date(specialPassData.goDate).toLocaleDateString()}</span>
                      <span className="hidden sm:inline"> | </span>
                      <span className="block sm:inline">Return: {new Date(specialPassData.returnDate).toLocaleDateString()}</span>
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Student Details Form */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card className="shadow-lg border-orange-200">
              <CardHeader className="bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-t-lg p-4 sm:p-6">
                <CardTitle className="text-lg sm:text-xl lg:text-2xl font-bold flex items-center gap-2">
                  <User className="w-5 h-5 sm:w-6 sm:h-6" />
                  Student Information
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 sm:p-6 lg:p-8">
                <div className="space-y-4 sm:space-y-6">
                  {/* Student Name */}
                  <div className="space-y-2">
                    <Label htmlFor="studentName" className="text-gray-700 font-semibold text-sm sm:text-base">
                      Student Name *
                    </Label>
                    <Input
                      id="studentName"
                      placeholder="e.g., John Doe, Mary Jane Smith"
                      value={studentName}
                      onChange={(e) => handleNameChange(e.target.value)}
                      className={`h-11 border-gray-300 focus:border-orange-500 focus:ring-orange-500 text-sm sm:text-base ${
                        nameError ? 'border-red-500' : ''
                      }`}
                    />
                    {nameError && (
                      <div className="flex items-start gap-2 text-xs sm:text-sm text-red-600">
                        <AlertCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
                        <span>{nameError}</span>
                      </div>
                    )}
                    <p className="text-xs text-gray-500">
                      Enter 2-3 names: first name + surname (+ optional middle name)
                    </p>
                  </div>

                  {/* Admission Number */}
                  <div className="space-y-2">
                    <Label htmlFor="admissionNumber" className="text-gray-700 font-semibold text-sm sm:text-base">
                      Admission Number *
                    </Label>
                    <Input
                      id="admissionNumber"
                      placeholder="e.g., 24CS094, 24MCA094, 24IMCA294"
                      value={admissionNumber}
                      onChange={(e) => handleAdmissionNumberChange(e.target.value)}
                      className={`h-11 border-gray-300 focus:border-orange-500 focus:ring-orange-500 text-sm sm:text-base ${
                        admissionError ? 'border-red-500' : ''
                      }`}
                    />
                    {admissionError && (
                      <div className="flex items-start gap-2 text-xs sm:text-sm text-red-600">
                        <AlertCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
                        <span>{admissionError}</span>
                      </div>
                    )}
                    <p className="text-xs text-gray-500">
                      Format: 2 digits + 2-4 uppercase letters + 3 digits
                    </p>
                  </div>

                  {/* Form Summary */}
                  {isFormValid && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3 }}
                      className="p-3 sm:p-4 bg-green-50 border border-green-200 rounded-lg"
                    >
                      <div className="flex items-center gap-2 text-green-700 font-semibold mb-2 text-sm sm:text-base">
                        <CheckCircle className="w-5 h-5" />
                        Student Information Valid
                      </div>
                      <div className="space-y-1 text-xs sm:text-sm">
                        <div>
                          <span className="font-medium text-gray-700">Name:</span>
                          <span className="ml-2 text-gray-900">{studentName.trim()}</span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Admission Number:</span>
                          <span className="ml-2 text-gray-900">{admissionNumber}</span>
                        </div>
                      </div>
                    </motion.div>
                  )}

                  {/* Next Button */}
                  <div className="mt-6 sm:mt-8">
                    <Button
                      onClick={handleNext}
                      disabled={!isFormValid || isLoading}
                      className="w-full h-12 sm:h-auto bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white font-semibold py-3 sm:py-4 rounded-lg transition-all duration-300 transform hover:scale-105 text-sm sm:text-base"
                    >
                      {isLoading ? (
                        <div className="flex items-center gap-2">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                          <span className="hidden sm:inline">Processing...</span>
                          <span className="sm:hidden">Processing...</span>
                        </div>
                      ) : (
                        <>
                          <span className="hidden sm:inline">Next: Select Bus Route</span>
                          <span className="sm:hidden">Next: Bus Route</span>
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </PageTransition>
  );
}

export default withAdminAuth(SpecialPassDetails);
