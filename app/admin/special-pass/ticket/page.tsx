'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PageTransition } from '@/components/ui/page-transition';
import { useSpecialPass } from '@/contexts/SpecialPassContext';
import { useAdmin, withAdminAuth } from '@/contexts/AdminContext';
import { motion, AnimatePresence } from 'framer-motion';
import { Ticket, Printer, Plus, Home, CheckCircle, Calendar, Bus, User, CreditCard, AlertTriangle } from 'lucide-react';
import { toast } from 'sonner';

function SpecialPassTicket() {
  const { user } = useAdmin();
  const router = useRouter();
  const { specialPassData, resetSpecialPassData } = useSpecialPass();
  const [currentDate] = useState(new Date());
  const [showModal, setShowModal] = useState(true);

  // Ensure flow validity
  useEffect(() => {
    if (!specialPassData.paymentMethod) {
      router.push('/admin/special-pass');
      return;
    }
  }, [specialPassData.paymentMethod, router]);

  const handlePrintTicket = () => {
    window.print();
  };

  const handleBookAnother = () => {
    resetSpecialPassData();
    router.push('/admin/special-pass');
    toast.info('Starting new special booking');
  };

  const handleBackToDashboard = () => {
    resetSpecialPassData();
    router.push('/admin/dashboard');
  };

  const getPaymentStatusDisplay = () => {
    if (specialPassData.paymentMethod === 'online') {
      return {
        status: 'PAID ONLINE',
        color: 'text-green-700',
        bgColor: 'bg-green-100',
        icon: <CheckCircle className="w-4 h-4" />
      };
    } else {
      return {
        status: 'PAY AT COLLEGE',
        color: 'text-orange-700',
        bgColor: 'bg-orange-100',
        icon: <CreditCard className="w-4 h-4" />
      };
    }
  };

  const paymentDisplay = getPaymentStatusDisplay();

  return (
    <PageTransition>
      <div className="min-h-screen p-3 sm:p-4 lg:p-6 bg-gradient-to-br from-orange-50 via-white to-red-50 print:min-h-0 print:p-3">
        {/* Important Ticket Modal */}
        <AnimatePresence>
          {showModal && (
            <div className="fixed inset-0 z-50 flex items-center justify-center p-3 sm:p-4 bg-black/50 print:hidden">
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.3 }}
                className="relative w-full max-w-md mx-auto"
                role="dialog"
                aria-modal="true"
                aria-labelledby="modal-title"
                aria-describedby="modal-description"
              >
                <Card className="shadow-2xl border-2 border-orange-300 bg-white">
                  <CardHeader className="bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-t-lg p-4 sm:p-6">
                    <CardTitle className="text-center flex items-center justify-center gap-2 text-base sm:text-lg">
                      <AlertTriangle className="w-5 h-5 sm:w-6 sm:h-6" />
                      <span>Important Notice</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-4 sm:p-6 text-center">
                    <div className="mb-4 sm:mb-6">
                      <p className="text-base sm:text-lg font-semibold text-gray-800 mb-2 sm:mb-3">
                        Please Download/Print your tickets and keep them on your device.
                      </p>
                      <p className="text-sm sm:text-base text-gray-600">
                        You won&apos;t see this screen again once you navigate away.
                      </p>
                    </div>

                    <div className="space-y-3">
                      <Button
                        onClick={handlePrintTicket}
                        className="w-full h-11 sm:h-auto bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white font-semibold py-3 rounded-lg transition-all duration-300 transform hover:scale-105 text-sm sm:text-base"
                      >
                        <Printer className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                        Print Ticket
                      </Button>

                      <Button
                        onClick={() => setShowModal(false)}
                        variant="outline"
                        className="w-full h-11 sm:h-auto border-orange-300 text-orange-700 hover:bg-orange-50 font-semibold py-3 rounded-lg transition-all duration-300 text-sm sm:text-base"
                      >
                        I Understand, Continue
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          )}
        </AnimatePresence>

        <div className="max-w-4xl mx-auto print:max-w-none print:mx-2">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-6 sm:mb-8 print:hidden"
          >
            <div className="flex flex-col sm:flex-row items-center justify-center gap-2 sm:gap-3 mb-3 sm:mb-4">
              <CheckCircle className="w-10 h-10 sm:w-12 sm:h-12 text-green-600" />
              <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-800 text-center">Special Booking Confirmed!</h1>
            </div>
            <p className="text-sm sm:text-base lg:text-lg text-gray-600">Your special bus pass has been successfully created</p>
          </motion.div>

          {/* Ticket */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-6 sm:mb-8 print:mb-2"
          >
            <Card className="shadow-2xl border-2 border-orange-300 bg-white print:shadow-md print:border-2 print:border-orange-400">
              <CardHeader className="bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-t-lg print:bg-gradient-to-r print:from-orange-600 print:to-red-600 print:py-4 p-4 sm:p-6">
                <CardTitle className="text-center">
                  <div className="flex flex-col sm:flex-row items-center justify-center gap-2 sm:gap-3 mb-2">
                    <Ticket className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 print:w-7 print:h-7" />
                    <span className="text-lg sm:text-xl lg:text-2xl font-bold print:text-xl leading-tight text-center">ST. JOSEPH&apos;S COLLEGE OF ENGINEERING AND TECHNOLOGY, PALAI</span>
                  </div>
                  <div className="text-base sm:text-lg print:text-base">Special Bus Pass Ticket</div>
                  <div className="text-xs sm:text-sm opacity-90 print:text-sm print:opacity-100">Admin Generated</div>
                  {specialPassData.booking_id && (
                    <div className="text-xs sm:text-sm font-mono bg-white bg-opacity-20 px-2 sm:px-3 py-1 rounded-full mt-2 print:bg-white print:bg-opacity-30">
                      Booking ID: {specialPassData.booking_id}
                    </div>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 sm:p-6 lg:p-8 print:p-5">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 print:gap-5">
                  {/* Left Column - All Information */}
                  <div className="lg:col-span-2 space-y-4 sm:space-y-6 print:space-y-4">
                    {/* Student Information */}
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 sm:p-4 print:bg-blue-50 print:p-3 print:border-blue-200">
                      <h3 className="font-semibold text-blue-800 mb-2 sm:mb-3 flex items-center gap-2 print:text-blue-800 print:mb-2 print:text-sm text-sm sm:text-base">
                        <User className="w-4 h-4 sm:w-5 sm:h-5 print:w-5 print:h-5" />
                        Student Information
                      </h3>
                      <div className="space-y-1 sm:space-y-2 text-xs sm:text-sm print:space-y-1 print:text-sm">
                        <div>
                          <span className="font-medium text-gray-700">Name:</span>
                          <span className="ml-2 text-gray-900 font-semibold block sm:inline">{specialPassData.studentName}</span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Admission No:</span>
                          <span className="ml-2 text-gray-900 font-semibold block sm:inline">{specialPassData.admissionNumber}</span>
                        </div>
                      </div>
                    </div>

                    {/* Travel Dates */}
                    <div className="bg-green-50 border border-green-200 rounded-lg p-3 sm:p-4 print:bg-green-50 print:p-3 print:border-green-200">
                      <h3 className="font-semibold text-green-800 mb-2 sm:mb-3 flex items-center gap-2 print:text-green-800 print:mb-2 print:text-sm text-sm sm:text-base">
                        <Calendar className="w-4 h-4 sm:w-5 sm:h-5 print:w-5 print:h-5" />
                        Travel Dates
                      </h3>
                      <div className="space-y-1 sm:space-y-2 text-xs sm:text-sm print:space-y-1 print:text-sm">
                        <div>
                          <span className="font-medium text-gray-700">Go Date:</span>
                          <span className="ml-2 text-gray-900 font-semibold block sm:inline">
                            {new Date(specialPassData.goDate).toLocaleDateString('en-US', {
                              weekday: 'short',
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric'
                            })}
                          </span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Return Date:</span>
                          <span className="ml-2 text-gray-900 font-semibold block sm:inline">
                            {new Date(specialPassData.returnDate).toLocaleDateString('en-US', {
                              weekday: 'short',
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric'
                            })}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Bus Information */}
                    <div className="bg-purple-50 border border-purple-200 rounded-lg p-3 sm:p-4 print:bg-purple-50 print:p-3 print:border-purple-200">
                      <h3 className="font-semibold text-purple-800 mb-2 sm:mb-3 flex items-center gap-2 print:text-purple-800 print:mb-2 print:text-sm text-sm sm:text-base">
                        <Bus className="w-4 h-4 sm:w-5 sm:h-5 print:w-5 print:h-5" />
                        Bus Information
                      </h3>
                      <div className="space-y-1 sm:space-y-2 text-xs sm:text-sm print:space-y-1 print:text-sm">
                        <div>
                          <span className="font-medium text-gray-700">Bus Name:</span>
                          <span className="ml-2 text-gray-900 font-semibold block sm:inline">{specialPassData.busName}</span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Route:</span>
                          <span className="ml-2 text-gray-900 font-semibold block sm:inline">{specialPassData.busRoute}</span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Destination:</span>
                          <span className="ml-2 text-gray-900 font-semibold block sm:inline">{specialPassData.destination}</span>
                        </div>
                      </div>
                    </div>

                    {/* Payment Information */}
                    <div className={`${paymentDisplay.bgColor} border rounded-lg p-3 sm:p-4 print:${paymentDisplay.bgColor} print:p-3 print:border`}>
                      <h3 className={`font-semibold ${paymentDisplay.color} mb-2 sm:mb-3 flex items-center gap-2 print:${paymentDisplay.color} print:mb-2 print:text-sm text-sm sm:text-base`}>
                        {paymentDisplay.icon}
                        Payment Information
                      </h3>
                      <div className="space-y-1 sm:space-y-2 text-xs sm:text-sm print:space-y-1 print:text-sm">
                        <div>
                          <span className="font-medium text-gray-700">Amount:</span>
                          <span className="ml-2 text-gray-900 font-bold text-base sm:text-lg print:text-base block sm:inline">₹{specialPassData.fare}</span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Status:</span>
                          <span className={`ml-2 font-bold ${paymentDisplay.color} print:${paymentDisplay.color} block sm:inline`}>
                            {paymentDisplay.status}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Footer Information */}
                <div className="mt-6 sm:mt-8 pt-4 sm:pt-6 border-t border-gray-200 print:mt-5 print:pt-4">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 text-xs text-gray-600 print:gap-3 print:text-xs mb-3 sm:mb-4">
                    <div>
                      <span className="font-medium">Booking ID:</span>
                      <span className="ml-1 text-blue-600 font-bold print:text-blue-600 block sm:inline">{specialPassData.booking_id || 'N/A'}</span>
                    </div>
                    <div>
                      <span className="font-medium">Booking Type:</span>
                      <span className="ml-1 text-orange-600 font-semibold print:text-orange-600 block sm:inline">SPECIAL ADMIN BOOKING</span>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 text-xs text-gray-600 print:gap-3 print:text-xs">
                    <div>
                      <span className="font-medium">Generated By:</span>
                      <span className="ml-1 block sm:inline">{user?.full_name}</span>
                    </div>
                    <div>
                      <span className="font-medium">Generated On:</span>
                      <span className="ml-1 block sm:inline">{currentDate.toLocaleDateString()} {currentDate.toLocaleTimeString()}</span>
                    </div>
                  </div>
                  <div className="mt-3 sm:mt-4 text-center print:mt-3">
                    <p className="text-xs text-gray-500 print:text-xs px-2">
                      This is a computer-generated ticket. Please carry a valid ID along with this pass.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="print:hidden"
          >
            <Card className="shadow-lg border-orange-200">
              <CardContent className="p-4 sm:p-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                  <Button
                    onClick={handlePrintTicket}
                    className="h-11 sm:h-auto bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 rounded-lg transition-all duration-300 transform hover:scale-105 text-sm sm:text-base"
                  >
                    <Printer className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                    <span className="hidden sm:inline">Print Ticket</span>
                    <span className="sm:hidden">Print</span>
                  </Button>

                  <Button
                    onClick={handleBookAnother}
                    className="h-11 sm:h-auto bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white font-semibold py-3 rounded-lg transition-all duration-300 transform hover:scale-105 text-sm sm:text-base sm:col-span-2 lg:col-span-1"
                  >
                    <Plus className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                    <span className="hidden sm:inline">Book Another Special Ticket</span>
                    <span className="sm:hidden">Book Another</span>
                  </Button>

                  <Button
                    onClick={handleBackToDashboard}
                    variant="outline"
                    className="h-11 sm:h-auto border-gray-300 text-gray-700 hover:bg-gray-50 font-semibold py-3 rounded-lg transition-all duration-300 transform hover:scale-105 text-sm sm:text-base sm:col-span-2 lg:col-span-1"
                  >
                    <Home className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                    <span className="hidden sm:inline">Back to Admin Dashboard</span>
                    <span className="sm:hidden">Dashboard</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </PageTransition>
  );
}

export default withAdminAuth(SpecialPassTicket);
