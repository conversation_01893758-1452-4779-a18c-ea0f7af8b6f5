'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PageTransition } from '@/components/ui/page-transition';
import { useSpecialPass } from '@/contexts/SpecialPassContext';
import { withAdminAuth } from '@/contexts/AdminContext';
import { motion } from 'framer-motion';
import { MapPin, ArrowLeft, CheckCircle, DollarSign, Clock } from 'lucide-react';
import { toast } from 'sonner';
import { supabase } from '@/lib/supabase';

interface RouteStop {
  id: number;
  stop_name: string;
  fare: number;
  stop_order: number;
}

interface Bus {
  name: string;
  route_code: string;
  available_seats: number | null;
}

interface Params {
  routeId: string;
}

function SpecialPassDestinations({ params }: { params: Params }) {
  const router = useRouter();
  const { specialPassData } = useSpecialPass();
  const [bus, setBus] = useState<Bus | null>(null);
  const [stops, setStops] = useState<RouteStop[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const routeCode = decodeURIComponent(params.routeId.replace('route-', ''));

  // Check if we have required data from previous steps
  useEffect(() => {
    if (!specialPassData.goDate || !specialPassData.returnDate) {
      toast.error('Please select travel dates first');
      router.push('/admin/special-pass');
      return;
    }

    if (!specialPassData.studentName || !specialPassData.admissionNumber) {
      toast.error('Please enter student details first');
      router.push('/admin/special-pass/details');
      return;
    }
  }, [specialPassData, router]);

  const fetchData = useCallback(async () => {
    try {
      const { data: busData } = await supabase
        .from('buses')
        .select('name, route_code, available_seats')
        .eq('route_code', routeCode)
        .eq('is_active', true)
        .single();

      const { data: stopsData } = await supabase
        .from('route_stops')
        .select('id, stop_name, fare, stop_order')
        .eq('route_code', routeCode)
        .eq('is_active', true)
        .order('stop_order');

      setBus(busData);
      setStops(stopsData || []);
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to load route information');
    } finally {
      setIsLoading(false);
    }
  }, [routeCode]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleDestinationSelect = (stop: RouteStop) => {
    router.push(`/admin/special-pass/buses/route-${routeCode}/destinations/${encodeURIComponent(stop.stop_name)}`);
  };

  if (isLoading) {
    return (
      <PageTransition>
        <div className="min-h-screen flex items-center justify-center p-3 sm:p-4 lg:p-6 bg-gradient-to-br from-orange-50 via-white to-red-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="w-full max-w-md"
          >
            <Card className="shadow-2xl border-0 bg-white/90 backdrop-blur-sm">
              <CardContent className="p-6 sm:p-8 text-center">
                <div className="animate-spin rounded-full h-10 w-10 sm:h-12 sm:w-12 border-b-2 border-orange-600 mx-auto mb-4"></div>
                <p className="text-sm sm:text-base text-gray-600">Loading destinations...</p>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </PageTransition>
    );
  }

  return (
    <PageTransition>
      <div className="min-h-screen p-3 sm:p-4 lg:p-6 bg-gradient-to-br from-orange-50 via-white to-red-50">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4 sm:gap-6 mb-6 sm:mb-8"
          >
            <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
              <Button
                onClick={() => router.back()}
                variant="outline"
                size="sm"
                className="h-11 w-fit"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
              <div>
                <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-800 mb-1 sm:mb-2 flex items-center gap-2 sm:gap-3">
                  <MapPin className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-orange-600" />
                  <span className="leading-tight">Select Destination</span>
                </h1>
                <p className="text-sm sm:text-base text-gray-600">Choose your destination for {bus?.name}</p>
              </div>
            </div>
          </motion.div>

          {/* Booking Summary */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-6 sm:mb-8"
          >
            <Card className="border-green-200 bg-green-50">
              <CardContent className="p-3 sm:p-4">
                <div className="flex items-start sm:items-center gap-3">
                  <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 sm:mt-0 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-green-800 text-sm sm:text-base">Booking Progress</h3>
                    <div className="text-xs sm:text-sm text-green-700 mt-1">
                      <div className="flex flex-col gap-1">
                        <span>{specialPassData.studentName} ({specialPassData.admissionNumber})</span>
                        <span>{bus?.name} ({routeCode})</span>
                        <span>{new Date(specialPassData.goDate).toLocaleDateString()} - {new Date(specialPassData.returnDate).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Destinations */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card className="shadow-lg border-orange-200">
              <CardHeader className="bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-t-lg p-4 sm:p-6">
                <CardTitle className="text-lg sm:text-xl lg:text-2xl font-bold flex items-center gap-2">
                  <MapPin className="w-5 h-5 sm:w-6 sm:h-6" />
                  Available Destinations
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 sm:p-6">
                {stops.length === 0 ? (
                  <div className="text-center py-8 sm:py-12">
                    <MapPin className="w-12 h-12 sm:w-16 sm:h-16 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-base sm:text-lg font-semibold text-gray-600 mb-2">No Destinations Available</h3>
                    <p className="text-sm sm:text-base text-gray-500">There are currently no active stops for this route.</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                    {stops.map((stop, index) => (
                      <motion.div
                        key={stop.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                      >
                        <Card
                          className="cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-105 border-2 border-gray-200 hover:border-orange-300 h-full"
                          onClick={() => handleDestinationSelect(stop)}
                        >
                          <CardContent className="p-4 sm:p-6 h-full flex flex-col">
                            <div className="flex items-center gap-3 mb-3 sm:mb-4">
                              <div className="p-2 bg-orange-100 rounded-lg flex-shrink-0">
                                <MapPin className="w-5 h-5 sm:w-6 sm:h-6 text-orange-600" />
                              </div>
                              <div className="flex-1 min-w-0">
                                <h3 className="font-bold text-base sm:text-lg text-gray-800 truncate">{stop.stop_name}</h3>
                                <div className="flex items-center gap-2 text-xs sm:text-sm text-gray-600">
                                  <Clock className="w-4 h-4 flex-shrink-0" />
                                  <span>Stop #{stop.stop_order}</span>
                                </div>
                              </div>
                            </div>

                            <div className="flex items-center justify-center mb-3 sm:mb-4 flex-1">
                              <div className="flex items-center gap-2">
                                <DollarSign className="w-5 h-5 text-green-600" />
                                <span className="text-xl sm:text-2xl font-bold text-green-700">₹{stop.fare}</span>
                              </div>
                            </div>

                            <Button
                              className="w-full h-10 sm:h-auto bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-sm sm:text-base"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDestinationSelect(stop);
                              }}
                            >
                              <span className="hidden sm:inline">Select Destination</span>
                              <span className="sm:hidden">Select</span>
                            </Button>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </PageTransition>
  );
}

export default withAdminAuth(SpecialPassDestinations);
