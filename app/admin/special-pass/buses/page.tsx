'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PageTransition } from '@/components/ui/page-transition';
import { useSpecialPass } from '@/contexts/SpecialPassContext';
import { useAdmin, withAdminAuth } from '@/contexts/AdminContext';
import { motion } from 'framer-motion';
import { Bus, ArrowLeft, CheckCircle, Users, MapPin } from 'lucide-react';
import { toast } from 'sonner';
import { supabase } from '@/lib/supabase';

interface BusRow {
  id: number;
  name: string;
  route_code: string;
  available_seats: number | null;
  is_active: boolean;
}

function SpecialPassBuses() {
  const { user } = useAdmin();
  const router = useRouter();
  const { specialPassData } = useSpecialPass();
  const [buses, setBuses] = useState<BusRow[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Check if we have required data from previous steps
  useEffect(() => {
    if (!specialPassData.goDate || !specialPassData.returnDate) {
      toast.error('Please select travel dates first');
      router.push('/admin/special-pass');
      return;
    }

    if (!specialPassData.studentName || !specialPassData.admissionNumber) {
      toast.error('Please enter student details first');
      router.push('/admin/special-pass/details');
      return;
    }
  }, [specialPassData, router]);

  useEffect(() => {
    fetchBuses();
  }, []);

  const fetchBuses = async () => {
    try {
      const { data, error } = await supabase
        .from('buses')
        .select('id, name, route_code, available_seats, is_active')
        .eq('is_active', true)
        .order('name');
      
      if (error) throw error;
      setBuses(data || []);
    } catch (error) {
      console.error('Error fetching buses:', error);
      toast.error('Failed to load buses');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBusSelect = (bus: BusRow) => {
    // For special pass, we don't check seat availability restrictions
    router.push(`/admin/special-pass/buses/route-${bus.route_code}/destinations`);
  };

  if (isLoading) {
    return (
      <PageTransition>
        <div className="min-h-screen flex items-center justify-center p-3 sm:p-4 lg:p-6 bg-gradient-to-br from-orange-50 via-white to-red-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="w-full max-w-md"
          >
            <Card className="shadow-2xl border-0 bg-white/90 backdrop-blur-sm">
              <CardContent className="p-6 sm:p-8 text-center">
                <div className="animate-spin rounded-full h-10 w-10 sm:h-12 sm:w-12 border-b-2 border-orange-600 mx-auto mb-4"></div>
                <p className="text-sm sm:text-base text-gray-600">Loading available buses...</p>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </PageTransition>
    );
  }

  return (
    <PageTransition>
      <div className="min-h-screen p-3 sm:p-4 lg:p-6 bg-gradient-to-br from-orange-50 via-white to-red-50">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4 sm:gap-6 mb-6 sm:mb-8"
          >
            <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
              <Button
                onClick={() => router.back()}
                variant="outline"
                size="sm"
                className="h-11 w-fit"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
              <div>
                <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-800 mb-1 sm:mb-2 flex items-center gap-2 sm:gap-3">
                  <Bus className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-orange-600" />
                  <span className="leading-tight">Select Bus Route</span>
                </h1>
                <p className="text-sm sm:text-base text-gray-600">Choose a bus route for the special booking</p>
              </div>
            </div>
          </motion.div>

          {/* Booking Summary */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-6 sm:mb-8"
          >
            <Card className="border-green-200 bg-green-50">
              <CardContent className="p-3 sm:p-4">
                <div className="flex items-start sm:items-center gap-3">
                  <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 sm:mt-0 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-green-800 text-sm sm:text-base">Booking Details</h3>
                    <div className="text-xs sm:text-sm text-green-700 mt-1">
                      <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                        <span>{specialPassData.studentName} ({specialPassData.admissionNumber})</span>
                        <span className="hidden sm:inline">|</span>
                        <span>{new Date(specialPassData.goDate).toLocaleDateString()} - {new Date(specialPassData.returnDate).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Bus Selection */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card className="shadow-lg border-orange-200">
              <CardHeader className="bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-t-lg p-4 sm:p-6">
                <CardTitle className="text-lg sm:text-xl lg:text-2xl font-bold flex items-center gap-2">
                  <Bus className="w-5 h-5 sm:w-6 sm:h-6" />
                  Available Bus Routes
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 sm:p-6">
                {buses.length === 0 ? (
                  <div className="text-center py-8 sm:py-12">
                    <Bus className="w-12 h-12 sm:w-16 sm:h-16 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-base sm:text-lg font-semibold text-gray-600 mb-2">No Buses Available</h3>
                    <p className="text-sm sm:text-base text-gray-500">There are currently no active bus routes.</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                    {buses.map((bus, index) => (
                      <motion.div
                        key={bus.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                      >
                        <Card
                          className="cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-105 border-2 border-gray-200 hover:border-orange-300 h-full"
                          onClick={() => handleBusSelect(bus)}
                        >
                          <CardContent className="p-4 sm:p-6 h-full flex flex-col">
                            <div className="flex items-center gap-3 mb-3 sm:mb-4">
                              <div className="p-2 bg-orange-100 rounded-lg flex-shrink-0">
                                <Bus className="w-5 h-5 sm:w-6 sm:h-6 text-orange-600" />
                              </div>
                              <div className="min-w-0 flex-1">
                                <h3 className="font-bold text-base sm:text-lg text-gray-800 truncate">{bus.name}</h3>
                                <p className="text-xs sm:text-sm text-gray-600">Route: {bus.route_code}</p>
                              </div>
                            </div>

                            <div className="space-y-2 flex-1">
                              <div className="flex items-center gap-2 text-xs sm:text-sm">
                                <Users className="w-4 h-4 text-gray-500 flex-shrink-0" />
                                <span className="text-gray-600">
                                  Available Seats: {bus.available_seats ?? 'N/A'}
                                </span>
                              </div>
                              <div className="flex items-center gap-2 text-xs sm:text-sm">
                                <MapPin className="w-4 h-4 text-gray-500 flex-shrink-0" />
                                <span className="text-gray-600">Active Route</span>
                              </div>
                            </div>

                            <Button
                              className="w-full mt-3 sm:mt-4 h-10 sm:h-auto bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-sm sm:text-base"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleBusSelect(bus);
                              }}
                            >
                              <span className="hidden sm:inline">Select This Route</span>
                              <span className="sm:hidden">Select Route</span>
                            </Button>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </PageTransition>
  );
}

export default withAdminAuth(SpecialPassBuses);
