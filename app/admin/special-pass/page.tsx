'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { PageTransition } from '@/components/ui/page-transition';
import { useSpecialPass } from '@/contexts/SpecialPassContext';
import { useAdmin, withAdminAuth } from '@/contexts/AdminContext';
import { motion } from 'framer-motion';
import { Ticket, Calendar, ArrowLeft, AlertTriangle } from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';

function SpecialPassLanding() {
  const { user } = useAdmin();
  const router = useRouter();
  const { specialPassData, updateSpecialPassData } = useSpecialPass();
  const [goDate, setGoDate] = useState(specialPassData.goDate || '');
  const [returnDate, setReturnDate] = useState(specialPassData.returnDate || '');
  const [isLoading, setIsLoading] = useState(false);

  // Get today's date in YYYY-MM-DD format for min date validation
  const today = new Date().toISOString().split('T')[0];

  const handleNext = () => {
    if (!goDate || !returnDate) {
      toast.error('Please select both go and return dates');
      return;
    }

    if (new Date(goDate) > new Date(returnDate)) {
      toast.error('Return date must be after go date');
      return;
    }

    setIsLoading(true);
    
    // Update special pass context with selected dates
    updateSpecialPassData({
      goDate,
      returnDate,
    });

    // Navigate to details page
    router.push('/admin/special-pass/details');
  };

  return (
    <PageTransition>
      <div className="min-h-screen p-3 sm:p-4 lg:p-6 bg-gradient-to-br from-orange-50 via-white to-red-50">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4 sm:gap-6 mb-6 sm:mb-8"
          >
            <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
              <Link href="/admin/dashboard">
                <Button variant="outline" size="sm" className="h-11 w-fit">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  <span className="hidden sm:inline">Back to Dashboard</span>
                  <span className="sm:hidden">Back</span>
                </Button>
              </Link>
              <div>
                <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-800 mb-1 sm:mb-2 flex items-center gap-2 sm:gap-3">
                  <Ticket className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-orange-600" />
                  <span className="leading-tight">Special Bus Pass Booking</span>
                </h1>
                <p className="text-sm sm:text-base text-gray-600">Create special bus pass bookings for exceptional cases</p>
              </div>
            </div>
            <div className="text-left sm:text-right">
              <p className="text-xs sm:text-sm text-gray-600">Admin: {user?.full_name}</p>
              <p className="text-xs text-gray-500">Special Booking Mode</p>
            </div>
          </motion.div>

          {/* Special Booking Notice */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-6 sm:mb-8"
          >
            <Card className="border-orange-200 bg-orange-50">
              <CardContent className="p-3 sm:p-4">
                <div className="flex items-start sm:items-center gap-3">
                  <AlertTriangle className="w-5 h-5 text-orange-600 mt-0.5 sm:mt-0 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-orange-800 text-sm sm:text-base">Special Booking Mode</h3>
                    <p className="text-xs sm:text-sm text-orange-700 mt-1">
                      This interface allows you to create bus pass bookings with custom dates and flexible payment options for special cases.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Date Selection */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card className="shadow-lg border-orange-200">
              <CardHeader className="bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-t-lg p-4 sm:p-6">
                <CardTitle className="text-lg sm:text-xl lg:text-2xl font-bold flex items-center gap-2">
                  <Calendar className="w-5 h-5 sm:w-6 sm:h-6" />
                  Select Travel Dates
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 sm:p-6 lg:p-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                  {/* Go Date */}
                  <div className="space-y-2">
                    <Label htmlFor="goDate" className="text-gray-700 font-semibold text-sm sm:text-base">
                      Go Date
                    </Label>
                    <Input
                      id="goDate"
                      type="date"
                      value={goDate}
                      onChange={(e) => setGoDate(e.target.value)}
                      min={today}
                      className="h-11 border-gray-300 focus:border-orange-500 focus:ring-orange-500 text-sm sm:text-base"
                    />
                  </div>

                  {/* Return Date */}
                  <div className="space-y-2">
                    <Label htmlFor="returnDate" className="text-gray-700 font-semibold text-sm sm:text-base">
                      Return Date
                    </Label>
                    <Input
                      id="returnDate"
                      type="date"
                      value={returnDate}
                      onChange={(e) => setReturnDate(e.target.value)}
                      min={goDate || today}
                      className="h-11 border-gray-300 focus:border-orange-500 focus:ring-orange-500 text-sm sm:text-base"
                    />
                  </div>
                </div>

                {/* Selected Dates Display */}
                {goDate && returnDate && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className="mt-4 sm:mt-6 p-3 sm:p-4 bg-green-50 border border-green-200 rounded-lg"
                  >
                    <h3 className="font-semibold text-green-800 mb-2 text-sm sm:text-base">Selected Dates</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4 text-xs sm:text-sm">
                      <div>
                        <span className="font-medium text-gray-700">Go Date:</span>
                        <span className="ml-2 text-gray-900 block sm:inline">
                          {new Date(goDate).toLocaleDateString('en-US', {
                            weekday: 'short',
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric'
                          })}
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Return Date:</span>
                        <span className="ml-2 text-gray-900 block sm:inline">
                          {new Date(returnDate).toLocaleDateString('en-US', {
                            weekday: 'short',
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric'
                          })}
                        </span>
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* Next Button */}
                <div className="mt-6 sm:mt-8">
                  <Button
                    onClick={handleNext}
                    disabled={!goDate || !returnDate || isLoading}
                    className="w-full h-12 sm:h-auto bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white font-semibold py-3 sm:py-4 rounded-lg transition-all duration-300 transform hover:scale-105 text-sm sm:text-base"
                  >
                    {isLoading ? (
                      <div className="flex items-center gap-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        <span className="hidden sm:inline">Processing...</span>
                        <span className="sm:hidden">Processing...</span>
                      </div>
                    ) : (
                      <>
                        <span className="hidden sm:inline">Next: Enter Student Details</span>
                        <span className="sm:hidden">Next: Student Details</span>
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </PageTransition>
  );
}

export default withAdminAuth(SpecialPassLanding);
