'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { PageTransition } from '@/components/ui/page-transition';
import { useAd<PERSON>, withAdminAuth } from '@/contexts/AdminContext';
import { Users, Plus, Edit, Trash2, Key, ArrowLeft, RefreshCw } from 'lucide-react';
import { motion } from 'framer-motion';
import { toast } from 'sonner';

interface StaffUser {
  id: number;
  username: string;
  route_code: string;
  created_at: string;
  updated_at: string;
}

interface Bus {
  route_code: string;
  name: string;
}

interface RouteAssignment {
  route_code: string;
  route_name: string;
  is_assigned: boolean;
  assigned_staff_username: string | null;
  assigned_staff_id: number | null;
}

const staffFormSchema = z.object({
  username: z.string().min(3, 'Username must be at least 3 characters'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  route_code: z.string().min(1, 'Route code is required'),
});

const editStaffFormSchema = z.object({
  username: z.string().min(3, 'Username must be at least 3 characters'),
  route_code: z.string().min(1, 'Route code is required'),
});

const passwordResetSchema = z.object({
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

type StaffFormData = z.infer<typeof staffFormSchema>;
type EditStaffFormData = z.infer<typeof editStaffFormSchema>;
type PasswordResetData = z.infer<typeof passwordResetSchema>;

function StaffManagement() {
  const { user } = useAdmin();
  const router = useRouter();
  const [staffUsers, setStaffUsers] = useState<StaffUser[]>([]);
  const [buses, setBuses] = useState<Bus[]>([]);
  const [routeAssignments, setRouteAssignments] = useState<RouteAssignment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false);
  const [selectedStaff, setSelectedStaff] = useState<StaffUser | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const addForm = useForm<StaffFormData>({
    resolver: zodResolver(staffFormSchema),
    defaultValues: {
      username: '',
      password: '',
      route_code: '',
    },
  });

  const editForm = useForm<EditStaffFormData>({
    resolver: zodResolver(editStaffFormSchema),
    defaultValues: {
      username: '',
      route_code: '',
    },
  });

  const passwordForm = useForm<PasswordResetData>({
    resolver: zodResolver(passwordResetSchema),
    defaultValues: {
      password: '',
    },
  });

  useEffect(() => {
    fetchStaffUsers();
    fetchBuses();
    fetchRouteAssignments();
  }, []);

  const fetchRouteAssignments = async () => {
    try {
      const response = await fetch('/api/admin/staff/route-assignments', {
        credentials: 'include'
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setRouteAssignments(result.data);
        }
      }
    } catch (error) {
      console.error('Error fetching route assignments:', error);
    }
  };

  const fetchStaffUsers = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/admin/staff', {
        credentials: 'include'
      });

      console.log('Staff API Response Status:', response.status);
      console.log('Staff API Response Headers:', Object.fromEntries(response.headers.entries()));

      if (response.ok) {
        const result = await response.json();
        console.log('Staff API Response Data:', result);
        
        if (result.success) {
          setStaffUsers(result.data);
          console.log('Staff users set successfully:', result.data);
        } else {
          console.error('Staff API response missing success field:', result);
          toast.error('Failed to fetch staff users');
        }
      } else {
        console.error('Staff API error response:', response.status, response.statusText);
        toast.error('Failed to fetch staff users');
      }
    } catch (error) {
      console.error('Error fetching staff users:', error);
      toast.error('Failed to fetch staff users');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchBuses = async () => {
    try {
      const response = await fetch('/api/admin/buses', {
        credentials: 'include'
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setBuses(result.data);
        }
      }
    } catch (error) {
      console.error('Error fetching buses:', error);
    }
  };

  const onAddSubmit = async (data: StaffFormData) => {
    setIsSubmitting(true);
    try {
      const response = await fetch('/api/admin/staff', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
        credentials: 'include',
      });

      console.log('Create Staff API Response Status:', response.status);
      console.log('Create Staff API Response Headers:', Object.fromEntries(response.headers.entries()));

      const result = await response.json();
      console.log('Create Staff API Response Data:', result);

      if (response.ok && result.success) {
        toast.success('Staff user created successfully');
        setIsAddModalOpen(false);
        addForm.reset();
        fetchStaffUsers();
        fetchRouteAssignments();
      } else {
        console.error('Create Staff API error:', result);
        toast.error(result.error || 'Failed to create staff user');
      }
    } catch (error) {
      console.error('Error creating staff user:', error);
      toast.error('Failed to create staff user');
    } finally {
      setIsSubmitting(false);
    }
  };

  const onEditSubmit = async (data: EditStaffFormData) => {
    if (!selectedStaff) return;

    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/admin/staff/${selectedStaff.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
        credentials: 'include',
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success('Staff user updated successfully');
        setIsEditModalOpen(false);
        setSelectedStaff(null);
        editForm.reset();
        fetchStaffUsers();
        fetchRouteAssignments();
      } else {
        toast.error(result.error || 'Failed to update staff user');
      }
    } catch (error) {
      toast.error('Failed to update staff user');
    } finally {
      setIsSubmitting(false);
    }
  };

  const onPasswordResetSubmit = async (data: PasswordResetData) => {
    if (!selectedStaff) return;

    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/admin/staff/${selectedStaff.id}/reset-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
        credentials: 'include',
      });

      console.log('Password Reset API Response Status:', response.status);
      console.log('Password Reset API Response Headers:', Object.fromEntries(response.headers.entries()));

      const result = await response.json();
      console.log('Password Reset API Response Data:', result);

      if (response.ok && result.success) {
        toast.success('Password reset successfully');
        setIsPasswordModalOpen(false);
        setSelectedStaff(null);
        passwordForm.reset();
      } else {
        console.error('Password Reset API error:', result);
        toast.error(result.error || 'Failed to reset password');
      }
    } catch (error) {
      console.error('Error resetting password:', error);
      toast.error('Failed to reset password');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = (staff: StaffUser) => {
    setSelectedStaff(staff);
    editForm.setValue('username', staff.username);
    editForm.setValue('route_code', staff.route_code);
    setIsEditModalOpen(true);
  };

  const handlePasswordReset = (staff: StaffUser) => {
    setSelectedStaff(staff);
    setIsPasswordModalOpen(true);
  };

  const handleDelete = async (staff: StaffUser) => {
    if (!confirm(`Are you sure you want to delete staff user "${staff.username}"?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/staff/${staff.id}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success('Staff user deleted successfully');
        fetchStaffUsers();
        fetchRouteAssignments();
      } else {
        toast.error(result.error || 'Failed to delete staff user');
      }
    } catch (error) {
      toast.error('Failed to delete staff user');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <PageTransition>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
        {/* Header - Mobile responsive */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center py-4 gap-4">
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 w-full sm:w-auto">
                <Button
                  onClick={() => router.push('/admin/dashboard')}
                  variant="outline"
                  size="sm"
                  className="w-full sm:w-auto min-h-[44px]"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Dashboard
                </Button>
                <div className="flex items-center space-x-3 mt-2 sm:mt-0">
                  <div className="w-10 h-10 bg-gradient-to-r from-teal-600 to-blue-600 rounded-full flex items-center justify-center">
                    <Users className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Staff Management</h1>
                    <p className="text-sm sm:text-base text-gray-600">Manage staff user accounts and permissions</p>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-3 w-full sm:w-auto">
                <Button
                  onClick={() => {
                    fetchStaffUsers();
                    fetchRouteAssignments();
                  }}
                  variant="outline"
                  size="sm"
                  disabled={isLoading}
                  className="flex-1 sm:flex-none min-h-[44px]"
                >
                  <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                  <span className="sm:inline">Refresh</span>
                </Button>
                <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
                  <DialogTrigger asChild>
                    <Button 
                      className="bg-gradient-to-r from-teal-600 to-blue-600 hover:from-teal-700 hover:to-blue-700 flex-1 sm:flex-none min-h-[44px]"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      <span className="sm:inline">Add Staff User</span>
                    </Button>
                  </DialogTrigger>
                </Dialog>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="space-y-4 sm:space-y-6"
          >
            {/* Route Assignment Summary - Mobile responsive */}
            <Card>
              <CardHeader className="p-4 md:p-6">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">R</span>
                  </div>
                  Route Assignment Status
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 md:p-6 pt-0">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3 sm:gap-4">
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-xl sm:text-2xl font-bold text-green-600">
                      {routeAssignments.filter(r => !r.is_assigned).length}
                    </div>
                    <div className="text-sm text-green-600">Available Routes</div>
                  </div>
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-xl sm:text-2xl font-bold text-blue-600">
                      {routeAssignments.filter(r => r.is_assigned).length}
                    </div>
                    <div className="text-sm text-blue-600">Assigned Routes</div>
                  </div>
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <div className="text-xl sm:text-2xl font-bold text-gray-600">
                      {routeAssignments.length}
                    </div>
                    <div className="text-sm text-gray-600">Total Routes</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Staff Users Table - Mobile responsive */}
            <Card>
              <CardHeader className="p-4 md:p-6">
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5" />
                  Staff Users ({staffUsers.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0 pb-4 md:p-6 md:pt-0">
                {isLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600"></div>
                  </div>
                ) : staffUsers.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    No staff users found. Add your first staff user to get started.
                  </div>
                ) : (
                  <div>
                    {/* Mobile card view for staff users */}
                    <div className="block md:hidden space-y-3 px-4 pt-2">
                      {staffUsers.map((staff) => {
                        const routeAssignment = routeAssignments.find(r => r.route_code === staff.route_code);
                        return (
                          <div 
                            key={staff.id}
                            className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
                          >
                            <div className="flex flex-col space-y-2 mb-3">
                              <div className="flex justify-between items-center">
                                <h3 className="font-medium text-gray-900">{staff.username}</h3>
                                <Badge variant="outline">{staff.route_code}</Badge>
                              </div>
                              <div className="text-xs text-gray-500">
                                Created: {new Date(staff.created_at).toLocaleDateString()}
                              </div>
                            </div>
                            
                            <div className="flex items-center justify-end space-x-2 pt-2 border-t border-gray-100">
                              <Button
                                onClick={() => handleEdit(staff)}
                                variant="outline"
                                size="sm"
                                className="min-h-[40px] flex-1"
                              >
                                <Edit className="w-4 h-4 mr-2" />
                                Edit
                              </Button>
                              <Button
                                onClick={() => handlePasswordReset(staff)}
                                variant="outline"
                                size="sm"
                                className="min-h-[40px] flex-1"
                              >
                                <Key className="w-4 h-4 mr-2" />
                                Reset
                              </Button>
                              <Button
                                onClick={() => handleDelete(staff)}
                                variant="outline"
                                size="sm"
                                className="text-red-600 hover:text-red-700 hover:bg-red-50 min-h-[40px] flex-1"
                              >
                                <Trash2 className="w-4 h-4 mr-2" />
                                Delete
                              </Button>
                            </div>
                          </div>
                        );
                      })}
                    </div>

                    {/* Desktop table view */}
                    <div className="hidden md:block overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Username</TableHead>
                            <TableHead>Route Code</TableHead>
                            <TableHead>Created At</TableHead>
                            <TableHead>Updated At</TableHead>
                            <TableHead>Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {staffUsers.map((staff) => {
                            const routeAssignment = routeAssignments.find(r => r.route_code === staff.route_code);
                            return (
                              <TableRow key={staff.id}>
                                <TableCell className="font-medium">{staff.username}</TableCell>
                                <TableCell>
                                  <Badge variant="outline">{staff.route_code}</Badge>
                                </TableCell>
                                <TableCell>{formatDate(staff.created_at)}</TableCell>
                                <TableCell>{formatDate(staff.updated_at)}</TableCell>
                                <TableCell>
                                  <div className="flex items-center space-x-2">
                                    <Button
                                      onClick={() => handleEdit(staff)}
                                      variant="outline"
                                      size="sm"
                                      className="min-h-[36px]"
                                    >
                                      <Edit className="w-4 h-4" />
                                    </Button>
                                    <Button
                                      onClick={() => handlePasswordReset(staff)}
                                      variant="outline"
                                      size="sm"
                                      className="min-h-[36px]"
                                    >
                                      <Key className="w-4 h-4" />
                                    </Button>
                                    <Button
                                      onClick={() => handleDelete(staff)}
                                      variant="outline"
                                      size="sm"
                                      className="text-red-600 hover:text-red-700 hover:bg-red-50 min-h-[36px]"
                                    >
                                      <Trash2 className="w-4 h-4" />
                                    </Button>
                                  </div>
                                </TableCell>
                              </TableRow>
                            );
                          })}
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Add Staff Modal - Mobile responsive */}
        <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
          <DialogContent className="sm:max-w-md max-w-[95vw] p-4 sm:p-6">
            <DialogHeader>
              <DialogTitle className="text-lg">Add New Staff User</DialogTitle>
            </DialogHeader>
            <Form {...addForm}>
              <form onSubmit={addForm.handleSubmit(onAddSubmit)} className="space-y-4">
                <FormField
                  control={addForm.control}
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Username</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          placeholder="Enter username" 
                          disabled={isSubmitting} 
                          className="min-h-[44px]"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={addForm.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          type="password" 
                          placeholder="Enter password" 
                          disabled={isSubmitting} 
                          className="min-h-[44px]"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={addForm.control}
                  name="route_code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Route Code</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isSubmitting}>
                        <FormControl>
                          <SelectTrigger className="min-h-[44px]">
                            <SelectValue placeholder="Select a route" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {routeAssignments
                            .slice()
                            .sort((a, b) => {
                              // Extract numeric parts from route codes
                              const numA = parseInt(a.route_code.replace('route-', '')) || 0;
                              const numB = parseInt(b.route_code.replace('route-', '')) || 0;
                              return numA - numB;
                            })
                            .map((route) => (
                              <SelectItem 
                                key={route.route_code} 
                                value={route.route_code}
                                disabled={route.is_assigned}
                                className="py-2"
                              >
                                <div className="flex flex-col sm:flex-row sm:items-center w-full">
                                  <div className="flex-1 min-w-0">
                                    <div className="font-medium text-sm">{route.route_code}</div>
                                    <div className="text-gray-600 text-xs truncate pr-2">{route.route_name}</div>
                                  </div>
                                  {route.is_assigned && (
                                    <Badge variant="secondary" className="mt-1 sm:mt-0 self-start sm:self-center text-xs whitespace-nowrap">
                                      Assigned
                                    </Badge>
                                  )}
                                </div>
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                      <div className="text-xs sm:text-sm text-gray-600 mt-1">
                        Only unassigned routes are available for selection
                      </div>
                    </FormItem>
                  )}
                />
                <div className="flex flex-col sm:flex-row sm:justify-end space-y-2 sm:space-y-0 sm:space-x-2 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsAddModalOpen(false)}
                    disabled={isSubmitting}
                    className="min-h-[44px] w-full sm:w-auto order-2 sm:order-1"
                  >
                    Cancel
                  </Button>
                  <Button 
                    type="submit" 
                    disabled={isSubmitting}
                    className="min-h-[44px] w-full sm:w-auto order-1 sm:order-2"
                  >
                    {isSubmitting ? 'Creating...' : 'Create Staff User'}
                  </Button>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>

        {/* Edit Staff Modal - Mobile responsive */}
        <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
          <DialogContent className="sm:max-w-md max-w-[95vw] p-4 sm:p-6">
            <DialogHeader>
              <DialogTitle className="text-lg">Edit Staff User</DialogTitle>
            </DialogHeader>
            <Form {...editForm}>
              <form onSubmit={editForm.handleSubmit(onEditSubmit)} className="space-y-4">
                <FormField
                  control={editForm.control}
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Username</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          placeholder="Enter username" 
                          disabled={isSubmitting} 
                          className="min-h-[44px]"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={editForm.control}
                  name="route_code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Route Code</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isSubmitting}>
                        <FormControl>
                          <SelectTrigger className="min-h-[44px]">
                            <SelectValue placeholder="Select a route" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {routeAssignments
                            .slice()
                            .sort((a, b) => {
                              // Extract numeric parts from route codes
                              const numA = parseInt(a.route_code.replace('route-', '')) || 0;
                              const numB = parseInt(b.route_code.replace('route-', '')) || 0;
                              return numA - numB;
                            })
                            .map((route) => (
                              <SelectItem 
                                key={route.route_code} 
                                value={route.route_code}
                                disabled={route.is_assigned && route.assigned_staff_id !== selectedStaff?.id}
                              >
                                <div className="flex flex-col sm:flex-row sm:items-center w-full">
                                  <div className="flex-1 min-w-0">
                                    <div className="font-medium text-sm">{route.route_code}</div>
                                    <div className="text-gray-600 text-xs truncate pr-2">{route.route_name}</div>
                                  </div>
                                  {route.is_assigned && route.assigned_staff_id !== selectedStaff?.id && (
                                    <Badge variant="secondary" className="mt-1 sm:mt-0 self-start sm:self-center text-xs whitespace-nowrap">
                                      Assigned
                                    </Badge>
                                  )}
                                  {route.is_assigned && route.assigned_staff_id === selectedStaff?.id && (
                                    <Badge variant="outline" className="mt-1 sm:mt-0 self-start sm:self-center text-xs whitespace-nowrap">
                                      Current
                                    </Badge>
                                  )}
                                </div>
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                      <div className="text-xs sm:text-sm text-gray-600 mt-1">
                        You can keep your current route or select an unassigned route
                      </div>
                    </FormItem>
                  )}
                />
                <div className="flex flex-col sm:flex-row sm:justify-end space-y-2 sm:space-y-0 sm:space-x-2 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsEditModalOpen(false)}
                    disabled={isSubmitting}
                    className="min-h-[44px] w-full sm:w-auto order-2 sm:order-1"
                  >
                    Cancel
                  </Button>
                  <Button 
                    type="submit" 
                    disabled={isSubmitting}
                    className="min-h-[44px] w-full sm:w-auto order-1 sm:order-2"
                  >
                    {isSubmitting ? 'Updating...' : 'Update Staff User'}
                  </Button>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>

        {/* Password Reset Modal - Mobile responsive */}
        <Dialog open={isPasswordModalOpen} onOpenChange={setIsPasswordModalOpen}>
          <DialogContent className="sm:max-w-md max-w-[95vw] p-4 sm:p-6">
            <DialogHeader>
              <DialogTitle className="text-lg">Reset Password</DialogTitle>
            </DialogHeader>
            <Form {...passwordForm}>
              <form onSubmit={passwordForm.handleSubmit(onPasswordResetSubmit)} className="space-y-4">
                <div className="text-sm text-gray-600 mb-4">
                  Reset password for: <strong>{selectedStaff?.username}</strong>
                </div>
                <FormField
                  control={passwordForm.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>New Password</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          type="password" 
                          placeholder="Enter new password" 
                          disabled={isSubmitting} 
                          className="min-h-[44px]"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="flex flex-col sm:flex-row sm:justify-end space-y-2 sm:space-y-0 sm:space-x-2 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsPasswordModalOpen(false)}
                    disabled={isSubmitting}
                    className="min-h-[44px] w-full sm:w-auto order-2 sm:order-1"
                  >
                    Cancel
                  </Button>
                  <Button 
                    type="submit" 
                    disabled={isSubmitting}
                    className="min-h-[44px] w-full sm:w-auto order-1 sm:order-2"
                  >
                    {isSubmitting ? 'Resetting...' : 'Reset Password'}
                  </Button>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>
    </PageTransition>
  );
}

export default withAdminAuth(StaffManagement);
