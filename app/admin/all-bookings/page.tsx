'use client';

import { useState, useEffect, useCallback } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { PageTransition } from '@/components/ui/page-transition';
import { useAdmin, withAdminAuth } from '@/contexts/AdminContext';
import { Database, ChevronLeft, ChevronRight, ArrowLeft, Crown, CheckCircle } from 'lucide-react';
import { motion } from 'framer-motion';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';

interface Booking {
  id: number;
  booking_id?: string;
  admission_number: string;
  student_name: string;
  bus_route: string;
  bus_name: string | null;
  destination: string;
  payment_status: boolean;
  created_at: string;
  fare: number | null;
  is_special: boolean | null;
  boarded: boolean;
}

interface PaginationData {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

function AllBookingsPage() {
  const { user } = useAdmin();
  const router = useRouter();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [pagination, setPagination] = useState<PaginationData>({
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 1
  });
  const [isLoading, setIsLoading] = useState(true);

  const fetchBookings = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/admin/bookings?page=${pagination.page}&limit=${pagination.limit}`, {
        credentials: 'include'
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setBookings(result.data.bookings);
          setPagination(prev => ({
            ...prev,
            ...result.data.pagination
          }));
        } else {
          toast.error('Failed to fetch bookings');
        }
      } else {
        toast.error('Failed to fetch bookings');
      }
    } catch (error) {
      console.error('Failed to fetch bookings:', error);
      toast.error('Failed to fetch bookings');
    } finally {
      setIsLoading(false);
    }
  }, [pagination.page, pagination.limit]);

  useEffect(() => {
    fetchBookings();
  }, [fetchBookings]);

  const formatCurrency = (amount: number | null) => {
    if (amount === null || amount === undefined) return '₹0.00';
    return `₹${amount.toFixed(2)}`;
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading bookings...</p>
        </div>
      </div>
    );
  }

  return (
    <PageTransition>
      <div className="min-h-screen p-3 md:p-4 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          {/* Header - Mobile responsive */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 md:mb-8"
          >
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 md:gap-4 w-full sm:w-auto">
              <Button
                variant="outline"
                onClick={() => router.push('/admin/dashboard')}
                className="flex items-center gap-2 w-full sm:w-auto min-h-[44px]"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to Dashboard
              </Button>
              <div className="mt-2 sm:mt-0">
                <h1 className="text-2xl md:text-3xl font-bold text-gray-800 mb-1 md:mb-2 flex items-center gap-2">
                  <Database className="w-6 h-6 md:w-8 md:h-8" />
                  All Bookings
                </h1>
                <p className="text-sm md:text-base text-gray-600">Complete list of all bookings in the system</p>
              </div>
            </div>
            <div className="flex items-center justify-between sm:justify-end w-full sm:w-auto mt-2 sm:mt-0 p-3 bg-blue-50 rounded-lg sm:bg-transparent sm:p-0">
              <span className="text-sm sm:hidden text-blue-700 font-medium">Total:</span>
              <div className="text-right">
                <p className="text-lg md:text-xl font-bold text-gray-800">
                  {pagination.total} <span className="text-sm font-normal text-gray-600">Bookings</span>
                </p>
              </div>
            </div>
          </motion.div>

          {/* Bookings Table - Mobile responsive */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card>
              <CardHeader className="p-4 md:p-6">
                <CardTitle className="flex items-center gap-2 text-lg md:text-xl">
                  <Database className="w-5 h-5" />
                  All Bookings ({pagination.total} total)
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0 pb-4 md:p-6 md:pt-0">
                {/* Mobile card view (displayed below 768px) */}
                <div className="block md:hidden px-4 space-y-4 mt-4">
                  {bookings.length === 0 ? (
                    <div className="text-center py-8">
                      <Database className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                      <p className="text-gray-500">No bookings found</p>
                    </div>
                  ) : (
                    bookings.map((booking) => (
                      <div 
                        key={booking.id} 
                        className={`border rounded-lg p-4 ${booking.boarded ? 'bg-green-50 border-green-200' : 'border-gray-200'}`}
                      >
                        <div className="flex justify-between items-start mb-3">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{booking.student_name}</span>
                            {booking.is_special && (
                              <div className="relative">
                                <Crown className="w-4 h-4 text-yellow-500" />
                              </div>
                            )}
                          </div>
                          <Badge variant="outline" className="font-mono text-xs">
                            {booking.booking_id || booking.id}
                          </Badge>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                          <div className="text-gray-500">Admission:</div>
                          <div>{booking.admission_number}</div>
                          
                          <div className="text-gray-500">Date:</div>
                          <div>{new Date(booking.created_at).toLocaleDateString()}</div>
                          
                          <div className="text-gray-500">Time:</div>
                          <div>{new Date(booking.created_at).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</div>
                          
                          <div className="text-gray-500">Route:</div>
                          <div><Badge variant="outline" className="font-normal">{booking.bus_name || booking.bus_route}</Badge></div>
                          
                          <div className="text-gray-500">Destination:</div>
                          <div className="truncate max-w-[120px]">{booking.destination}</div>
                          
                          <div className="text-gray-500">Fare:</div>
                          <div className="font-medium">{formatCurrency(booking.fare)}</div>
                          
                          <div className="text-gray-500">Status:</div>
                          <div>
                            {booking.boarded ? (
                              <Badge variant="default" className="bg-green-600 hover:bg-green-700 text-white font-normal">
                                <CheckCircle className="w-3 h-3 mr-1" />
                                Boarded
                              </Badge>
                            ) : (
                              <Badge variant="secondary" className="text-slate-600 font-normal">
                                Not Boarded
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>

                {/* Desktop table view (displayed at 768px and above) */}
                <div className="hidden md:block">
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Booking ID</TableHead>
                          <TableHead>Name</TableHead>
                          <TableHead>Admission Number</TableHead>
                          <TableHead>Date and Time Booked</TableHead>
                          <TableHead>Route Name</TableHead>
                          <TableHead>Destination</TableHead>
                          <TableHead>Fare</TableHead>
                          <TableHead>Boarding Status</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {bookings.map((booking) => (
                          <TableRow
                            key={booking.id}
                            className={booking.boarded ? 'bg-green-50 border-green-200' : ''}
                          >
                            <TableCell className="font-mono text-sm font-medium">
                              {booking.booking_id || booking.id}
                            </TableCell>
                            <TableCell className="font-medium flex items-center gap-2">
                              {booking.student_name}
                              {booking.is_special && (
                                <div className="relative group">
                                  <Crown className="w-4 h-4 text-yellow-500" />
                                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-800 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                                    Admin Booked
                                  </div>
                                </div>
                              )}
                            </TableCell>
                            <TableCell>{booking.admission_number}</TableCell>
                            <TableCell>{formatDateTime(booking.created_at)}</TableCell>
                            <TableCell>
                              <Badge variant="outline">{booking.bus_name || booking.bus_route}</Badge>
                            </TableCell>
                            <TableCell>{booking.destination}</TableCell>
                            <TableCell className="font-medium">
                              {formatCurrency(booking.fare)}
                            </TableCell>
                            <TableCell>
                              {booking.boarded ? (
                                <Badge variant="default" className="bg-green-600 hover:bg-green-700 text-white">
                                  <CheckCircle className="w-3 h-3 mr-1" />
                                  Boarded
                                </Badge>
                              ) : (
                                <Badge variant="secondary" className="text-slate-600">
                                  Not Boarded
                                </Badge>
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
                
                {/* Empty state message for desktop view */}
                {bookings.length === 0 && (
                  <div className="hidden md:block text-center py-8">
                    <Database className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                    <p className="text-gray-500">No bookings found</p>
                  </div>
                )}
                
                {/* Pagination - Mobile responsive */}
                {pagination.totalPages > 1 && (
                  <div className="flex flex-col sm:flex-row items-center justify-between gap-3 px-4 md:px-0 py-4">
                    <div className="text-xs sm:text-sm text-gray-500 text-center sm:text-left w-full sm:w-auto">
                      Page {pagination.page} of {pagination.totalPages} 
                      <span className="hidden sm:inline"> ({pagination.total} total bookings)</span>
                    </div>
                    <div className="flex space-x-2 w-full sm:w-auto justify-center sm:justify-end">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(Math.max(1, pagination.page - 1))}
                        disabled={pagination.page === 1}
                        className="min-h-[40px] px-3 sm:px-4 flex-1 sm:flex-initial"
                      >
                        <ChevronLeft className="w-4 h-4 sm:mr-1" />
                        <span className="hidden sm:inline">Previous</span>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(Math.min(pagination.totalPages, pagination.page + 1))}
                        disabled={pagination.page === pagination.totalPages}
                        className="min-h-[40px] px-3 sm:px-4 flex-1 sm:flex-initial"
                      >
                        <span className="hidden sm:inline">Next</span>
                        <ChevronRight className="w-4 h-4 sm:ml-1" />
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </PageTransition>
  );
}

export default withAdminAuth(AllBookingsPage);
