"use client";

import { toast } from "react-toastify";

export type FetchWithToastOptions = RequestInit & {
  pendingMessage?: string;
  delayMs?: number;
  timeoutMs?: number;
};

export async function fetchWithToast<T = unknown>(
  input: RequestInfo | URL,
  { pendingMessage = "Loading...", delayMs = 3000, timeoutMs, ...init }: FetchWithToastOptions = {}
): Promise<T> {
  let toastId: React.ReactText | undefined;
  let didFinish = false;

  const showToastTimer = setTimeout(() => {
    if (!didFinish) {
      toastId = toast.info(pendingMessage, {
        isLoading: true,
        autoClose: false,
        closeOnClick: false,
        draggable: false,
      });
    }
  }, delayMs);

  const controller = timeoutMs ? new AbortController() : undefined;
  const timer = timeoutMs ? setTimeout(() => controller?.abort(), timeoutMs) : undefined;

  try {
    const response = await fetch(input, {
      ...init,
      signal: controller?.signal ?? init?.signal,
    });
    didFinish = true;
    clearTimeout(showToastTimer);
    if (timer) clearTimeout(timer);
    if (toastId) toast.dismiss(toastId);

    if (!response.ok) {
      const text = await response.text().catch(() => "");
      throw new Error(text || `Request failed with status ${response.status}`);
    }

    const contentType = response.headers.get("content-type") || "";
    if (contentType.includes("application/json")) {
      return (await response.json()) as T;
    }
    // Non-JSON fallback
    return (await response.text()) as T;
  } catch (error) {
    didFinish = true;
    clearTimeout(showToastTimer);
    if (timer) clearTimeout(timer);
    if (toastId) toast.dismiss(toastId);
    throw error;
  }
}