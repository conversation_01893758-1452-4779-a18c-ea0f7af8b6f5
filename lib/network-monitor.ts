'use client';

import { toast } from 'sonner';

// Configuration
const SLOW_NETWORK_THRESHOLD = 5500; // 5.5 seconds in milliseconds
const TOAST_MESSAGE = "Your network is slow!! Please check your internet!!";

// Track ongoing requests to avoid duplicate toasts for the same request
const ongoingRequests = new Map<string, number>();

// Store original fetch function
const originalFetch = globalThis.fetch;

/**
 * Enhanced fetch function that monitors network delays
 */
async function monitoredFetch(
  input: RequestInfo | URL,
  init?: RequestInit
): Promise<Response> {
  // Generate unique request ID for tracking
  const requestId = generateRequestId(input, init);
  const startTime = Date.now();
  
  // Store request start time
  ongoingRequests.set(requestId, startTime);
  
  try {
    // Execute the original fetch request
    const response = await originalFetch(input, init);
    
    // Calculate request duration
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Check if request exceeded threshold
    if (duration > SLOW_NETWORK_THRESHOLD) {
      showSlowNetworkToast(duration);
    }
    
    // Clean up tracking
    ongoingRequests.delete(requestId);
    
    return response;
  } catch (error) {
    // Calculate duration even for failed requests
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Check if request exceeded threshold before failing
    if (duration > SLOW_NETWORK_THRESHOLD) {
      showSlowNetworkToast(duration);
    }
    
    // Clean up tracking
    ongoingRequests.delete(requestId);
    
    // Re-throw the original error
    throw error;
  }
}

/**
 * Generate a unique request ID for tracking
 */
function generateRequestId(input: RequestInfo | URL, init?: RequestInit): string {
  const url = typeof input === 'string' ? input : input.toString();
  const method = init?.method || 'GET';
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(7);
  
  return `${method}-${url}-${timestamp}-${random}`;
}

/**
 * Show slow network toast notification
 */
function showSlowNetworkToast(duration: number): void {
  // Use Sonner toast with warning styling
  toast.warning(TOAST_MESSAGE, {
    duration: 4000, // Show for 4 seconds
    position: 'top-right',
    style: {
      backgroundColor: '#fef3c7', // Light yellow background
      borderColor: '#f59e0b', // Orange border
      color: '#92400e', // Dark orange text
    },
    // Add duration info for debugging (can be removed in production)
    description: `Request took ${(duration / 1000).toFixed(1)}s to complete`,
  });
  
  // Log for debugging purposes
  console.warn(`Slow network detected: Request took ${duration}ms to complete`);
}

/**
 * Initialize network monitoring by replacing global fetch
 */
export function initializeNetworkMonitoring(): void {
  // Only initialize once and only in browser environment
  if (typeof window === 'undefined') {
    return; // Skip on server-side
  }
  
  // Check if already initialized
  if (globalThis.fetch !== originalFetch) {
    return; // Already initialized
  }
  
  // Replace global fetch with monitored version
  globalThis.fetch = monitoredFetch;
  
  console.log('Network monitoring initialized - tracking API request delays');
}

/**
 * Disable network monitoring (restore original fetch)
 */
export function disableNetworkMonitoring(): void {
  if (typeof window === 'undefined') {
    return;
  }
  
  // Restore original fetch
  globalThis.fetch = originalFetch;
  
  // Clear any ongoing request tracking
  ongoingRequests.clear();
  
  console.log('Network monitoring disabled');
}

/**
 * Get current monitoring statistics
 */
export function getMonitoringStats(): {
  isActive: boolean;
  ongoingRequests: number;
  threshold: number;
} {
  return {
    isActive: globalThis.fetch !== originalFetch,
    ongoingRequests: ongoingRequests.size,
    threshold: SLOW_NETWORK_THRESHOLD,
  };
}

/**
 * Update the slow network threshold
 */
export function updateThreshold(newThreshold: number): void {
  if (newThreshold > 0) {
    // We can't directly modify the const, but we could implement this
    // by storing the threshold in a variable instead of a const
    console.warn('Threshold update not implemented in current version');
  }
}
