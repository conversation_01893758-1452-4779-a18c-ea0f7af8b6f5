import { NextRequest } from 'next/server';
import { validateSession, AdminUser } from './auth';

/**
 * Check admin authentication from request cookies
 * Returns the admin user if authenticated, null otherwise
 */
export async function checkAdminAuth(request: NextRequest): Promise<AdminUser | null> {
  try {
    // Get the admin token from cookies
    const adminToken = request.cookies.get('admin_token')?.value;
    
    if (!adminToken) {
      return null;
    }

    // Validate the session
    const adminUser = await validateSession(adminToken);
    
    return adminUser;
  } catch (error) {
    console.error('Admin auth check error:', error);
    return null;
  }
}

/**
 * Extract admin token from Authorization header or cookies
 */
export function extractAdminToken(request: NextRequest): string | null {
  // Try Authorization header first
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // Fall back to cookies
  return request.cookies.get('admin_token')?.value || null;
}

/**
 * Check if the request is from an authenticated admin
 * Throws an error if not authenticated
 */
export async function requireAdminAuth(request: NextRequest): Promise<AdminUser> {
  const adminUser = await checkAdminAuth(request);
  
  if (!adminUser) {
    throw new Error('Admin authentication required');
  }
  
  return adminUser;
}

/**
 * Check if admin has specific role
 */
export function hasAdminRole(adminUser: AdminUser, requiredRole: string): boolean {
  return adminUser.role === requiredRole || adminUser.role === 'super_admin';
}

/**
 * Check if admin has any of the specified roles
 */
export function hasAnyAdminRole(adminUser: AdminUser, roles: string[]): boolean {
  return roles.includes(adminUser.role) || adminUser.role === 'super_admin';
}
