import { NextRequest, NextResponse } from 'next/server';
import { validateSession, validateStaffSession, AdminUser, StaffUser } from './auth';

export interface AuthenticatedRequest extends NextRequest {
  user?: AdminUser;
}

export interface StaffAuthenticatedRequest extends NextRequest {
  staff?: StaffUser;
}

/**
 * Middleware to validate admin authentication
 */
export async function withAuth(
  request: NextRequest,
  handler: (req: AuthenticatedRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  try {
    // Get token from Authorization header or cookie
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '') || 
                  request.cookies.get('admin_token')?.value;

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Validate session
    const user = await validateSession(token);
    if (!user) {
      return NextResponse.json(
        { error: 'Invalid or expired session' },
        { status: 401 }
      );
    }

    // Add user to request
    const authenticatedRequest = request as AuthenticatedRequest;
    authenticatedRequest.user = user;

    return handler(authenticatedRequest);
  } catch (error) {
    console.error('Authentication middleware error:', error);
    return NextResponse.json(
      { error: 'Authentication failed' },
      { status: 500 }
    );
  }
}

/**
 * Middleware to validate admin role
 */
export async function withAdminAuth(
  request: NextRequest,
  handler: (req: AuthenticatedRequest) => Promise<NextResponse>,
  requiredRole: string = 'admin'
): Promise<NextResponse> {
  return withAuth(request, async (req: AuthenticatedRequest) => {
    const user = req.user!;
    
    // Check if user has required role
    if (requiredRole === 'super_admin' && user.role !== 'super_admin') {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    return handler(req);
  });
}

/**
 * Extract user from authenticated request
 */
export function getUser(request: AuthenticatedRequest): AdminUser | null {
  return request.user || null;
}

// ============================================================================
// STAFF AUTHENTICATION MIDDLEWARE
// ============================================================================

/**
 * Middleware to validate staff authentication
 */
export async function withStaffAuth(
  request: NextRequest,
  handler: (req: StaffAuthenticatedRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  try {
    // Get token from Authorization header or cookie
    const authHeader = request.headers.get('authorization');
    let token = authHeader?.replace('Bearer ', '');
    
    // If no Authorization header, try to get from cookie
    if (!token) {
      const cookieToken = request.cookies.get('staff_token')?.value;
      if (cookieToken) {
        token = cookieToken;
      }
    }

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Validate session
    const staff = await validateStaffSession(token);
    if (!staff) {
      return NextResponse.json(
        { error: 'Invalid or expired session' },
        { status: 401 }
      );
    }

    // Add staff to request
    const authenticatedRequest = request as StaffAuthenticatedRequest;
    authenticatedRequest.staff = staff;

    return handler(authenticatedRequest);
  } catch (error) {
    console.error('Staff authentication middleware error:', error);
    return NextResponse.json(
      { error: 'Authentication failed' },
      { status: 500 }
    );
  }
}

/**
 * Extract staff from authenticated request
 */
export function getStaff(request: StaffAuthenticatedRequest): StaffUser | null {
  return request.staff || null;
}

/**
 * Set staff authentication cookie
 */
export function setStaffAuthCookie(response: NextResponse, token: string): void {
  response.cookies.set('staff_token', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 24 * 60 * 60, // 24 hours
    path: '/'
  });
}

/**
 * Clear staff authentication cookie
 */
export function clearStaffAuthCookie(response: NextResponse): void {
  response.cookies.set('staff_token', '', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 0,
    path: '/'
  });
}

/**
 * Validate request body against schema
 */
export function validateRequestBody<T>(
  body: any,
  requiredFields: (keyof T)[]
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  for (const field of requiredFields) {
    // Only check for presence (not truthiness) so that false/0/'' are accepted when intended
    const hasField = Object.prototype.hasOwnProperty.call(body, field as string);
    const value = (body as any)[field as string];
    if (!hasField || value === undefined || value === null) {
      errors.push(`${String(field)} is required`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Create standardized API response
 */
export function createApiResponse<T>(
  data?: T,
  error?: string,
  status: number = 200
): NextResponse {
  if (error) {
    return NextResponse.json({ error }, { status });
  }

  return NextResponse.json({ data, success: true }, { status });
}

/**
 * Handle API errors consistently
 */
export function handleApiError(error: any, defaultMessage: string = 'Internal server error'): NextResponse {
  console.error('API Error:', error);
  
  // If it's a known error with a message, use that
  if (error?.message) {
    return NextResponse.json(
      { error: error.message },
      { status: error.status || 500 }
    );
  }

  // Otherwise use default message
  return NextResponse.json(
    { error: defaultMessage },
    { status: 500 }
  );
}

/**
 * Set secure cookie for admin token
 */
export function setAuthCookie(response: NextResponse, token: string): void {
  response.cookies.set('admin_token', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 24 * 60 * 60, // 24 hours
    path: '/'
  });
}

/**
 * Clear auth cookie
 */
export function clearAuthCookie(response: NextResponse): void {
  response.cookies.set('admin_token', '', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 0,
    path: '/'
  });
}

/**
 * Utility function to check if booking is enabled
 * This is a critical security function that should be called before any booking operations
 */
export async function checkBookingStatus(supabaseAdmin: any) {
  const { data: adminSettings, error: settingsError } = await supabaseAdmin
    .from('admin_settings')
    .select('booking_enabled')
    .single();

  if (settingsError) {
    console.error('Error fetching admin settings:', settingsError);
    throw new Error('Unable to verify booking status - System configuration error');
  }

  const bookingEnabled = adminSettings?.booking_enabled ?? false;
  
  if (!bookingEnabled) {
    console.log('Booking operation rejected - booking is disabled');
    throw new Error('Booking is currently disabled - Please try again later when booking is enabled');
  }

  return true;
}
