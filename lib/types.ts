export interface Bus {
  id: number;
  name: string;
  route: string;
}

export interface RouteStop {
  id: number;
  name: string;
  fare: number;
}

export interface Booking {
  booking_id: string;
  studentName: string;
  admissionNumber: string;
  busRoute: string;
  destination: string;
  paymentStatus: boolean;
  timestamp: string;
  goDate?: string | null;
  returnDate?: string | null;
  fare?: number | null;
  busName?: string | null;
  boarded?: boolean;
}

export interface CreateBookingRequest {
  booking_id: string;
  studentName: string;
  admissionNumber: string;
  busRoute: string;
  destination: string;
  paymentStatus?: boolean;
  timestamp: string;
  razorpay_payment_id?: string;
  razorpay_order_id?: string;
  razorpay_signature?: string;
}

export interface AdminSettings {
  bookingEnabled: boolean;
  goDate: string | null;
  returnDate: string | null;
  busAvailability: { [busRoute: string]: number };
}

export interface NewBookingStats {
  totalBuses: number;
  totalBookings: number;
  currentBookings: number;
  paidBookings: number;
  unpaidBookings: number;
  currentRevenue: number;
  availableSeats: number;
  totalCapacity: number;
  occupancyRate: string;
}