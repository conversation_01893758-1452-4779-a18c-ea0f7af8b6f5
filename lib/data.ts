import { Bus, RouteStop } from './types';

export const buses: Bus[] = [
  { id: 1, name: "Bus 1", route: "route-1" },
  { id: 2, name: "Bus 2", route: "route-2" },
  { id: 3, name: "Bus 3", route: "route-3" },
  { id: 4, name: "Bus 4", route: "route-4" },
  { id: 5, name: "Bus 5", route: "route-5" },
  { id: 6, name: "Bus 6", route: "route-6" },
  { id: 7, name: "Bus 7", route: "route-7" },
  { id: 8, name: "Bus 8", route: "route-8" },
  { id: 9, name: "Bus 9", route: "route-9" },
  { id: 10, name: "Bus 10", route: "route-10" },
  { id: 11, name: "Bus 11", route: "route-11" },
  { id: 12, name: "Bus 12", route: "route-12" },
  { id: 13, name: "Bus 13", route: "route-13" },
  { id: 14, name: "Bus 14", route: "route-14" },
  { id: 15, name: "Bus 15", route: "route-15" },
  { id: 16, name: "Bus 16", route: "route-16" },
  { id: 17, name: "Bus 17", route: "route-17" },
  { id: 18, name: "Bus 18", route: "route-18" },
  { id: 19, name: "Bus 19", route: "route-19" },
  { id: 20, name: "Bus 20", route: "route-20" },
  { id: 21, name: "Bus 21", route: "route-21" },
  { id: 22, name: "Bus 22", route: "route-22" },
];

export const routeStops: { [key: string]: RouteStop[] } = {
  "route-1": [
    { id: 1, name: "Kottayam", fare: 50 },
    { id: 2, name: "Changanassery", fare: 40 },
    { id: 3, name: "Thiruvalla", fare: 60 },
    { id: 4, name: "Chengannur", fare: 70 },
  ],
  "route-2": [
    { id: 1, name: "Ernakulam", fare: 80 },
    { id: 2, name: "Aluva", fare: 70 },
    { id: 3, name: "Perumbavoor", fare: 60 },
    { id: 4, name: "Muvattupuzha", fare: 50 },
  ],
  "route-3": [
    { id: 1, name: "Thodupuzha", fare: 45 },
    { id: 2, name: "Idukki", fare: 65 },
    { id: 3, name: "Kumily", fare: 85 },
    { id: 4, name: "Vandiperiyar", fare: 75 },
  ],
  // Add more routes as needed
};

// Generate default routes for remaining buses
for (let i = 4; i <= 22; i++) {
  const busRoute = `route-${i}`;
  routeStops[busRoute] = [
    { id: 1, name: `Destination ${i}-1`, fare: 40 + (i * 2) },
    { id: 2, name: `Destination ${i}-2`, fare: 50 + (i * 2) },
    { id: 3, name: `Destination ${i}-3`, fare: 60 + (i * 2) },
    { id: 4, name: `Destination ${i}-4`, fare: 70 + (i * 2) },
  ];
}