import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Generates a unique booking ID with the format: BK + route_code_as_2_digits + stop_number_as_1_digit + random_4_chars
 * 
 * @param busRoute - The bus route (e.g., "route-1", "route-12")
 * @param destination - The destination/stop name
 * @returns A unique booking ID string
 * 
 * @example
 * generateBookingId("route-1", "Stop A") // Returns: "BK0127X9K"
 * generateBookingId("route-12", "Stop B") // Returns: "BK1223A5M"
 */
export function generateBookingId(busRoute: string, destination: string): string {
  // Extract route number from bus route (e.g., "route-1" -> "01", "route-12" -> "12")
  const routeMatch = busRoute.match(/route-(\d+)/);
  if (!routeMatch) {
    throw new Error(`Invalid bus route format: ${busRoute}. Expected format: "route-{number}"`);
  }
  
  const routeNumber = parseInt(routeMatch[1], 10);
  const routeCode = routeNumber.toString().padStart(2, '0'); // Ensure 2 digits
  
  // Extract stop number from destination
  // This is a simplified mapping - you may need to adjust based on your actual stop naming convention
  const stopNumber = extractStopNumber(destination);
  
  // Generate random 4 characters (mix of digits and uppercase letters)
  const randomChars = generateRandomString(4);
  
  return `BK${routeCode}${stopNumber}${randomChars}`;
}

/**
 * Extracts stop number from destination name
 * This is a simplified implementation - adjust based on your actual stop naming convention
 */
function extractStopNumber(destination: string): string {
  // Try to extract number from destination name
  const numberMatch = destination.match(/(\d+)/);
  if (numberMatch) {
    return numberMatch[1];
  }
  
  // Fallback: use first character code if no number found
  const firstChar = destination.charAt(0).toUpperCase();
  const charCode = firstChar.charCodeAt(0) - 64; // A=1, B=2, etc.
  return Math.min(charCode, 9).toString(); // Cap at 9 for single digit
}

/**
 * Generates a random string of specified length with mix of digits and uppercase letters
 */
function generateRandomString(length: number): string {
  const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * chars.length);
    result += chars[randomIndex];
  }
  
  return result;
}
