'use client';

import { createContext, useContext, useState, useCallback, ReactNode } from 'react';

interface SpecialPassData {
  // Custom dates (no booking control restrictions)
  goDate: string;
  returnDate: string;

  // Student information (manual entry for special cases)
  studentName: string;
  admissionNumber: string;

  // Bus and destination selection
  busRoute: string;
  busName: string;
  destination: string;
  fare: number;

  // Payment information
  paymentStatus: boolean;
  paymentMethod: 'online' | 'offline' | null;

  // Booking identification
  booking_id: string;
}

interface SpecialPassContextType {
  specialPassData: SpecialPassData;
  updateSpecialPassData: (data: Partial<SpecialPassData>) => void;
  resetSpecialPassData: () => void;
}

const initialSpecialPassData: SpecialPassData = {
  goDate: '',
  returnDate: '',
  studentName: '',
  admissionNumber: '',
  busRoute: '',
  busName: '',
  destination: '',
  fare: 0,
  paymentStatus: false,
  paymentMethod: null,
  booking_id: '',
};

const SpecialPassContext = createContext<SpecialPassContextType | undefined>(undefined);

export function SpecialPassProvider({ children }: { children: ReactNode }) {
  const [specialPassData, setSpecialPassData] = useState<SpecialPassData>(initialSpecialPassData);

  const updateSpecialPassData = useCallback((data: Partial<SpecialPassData>) => {
    setSpecialPassData(prev => ({ ...prev, ...data }));
  }, []);

  const resetSpecialPassData = useCallback(() => {
    setSpecialPassData(initialSpecialPassData);
  }, []);

  return (
    <SpecialPassContext.Provider value={{ 
      specialPassData, 
      updateSpecialPassData, 
      resetSpecialPassData 
    }}>
      {children}
    </SpecialPassContext.Provider>
  );
}

export function useSpecialPass() {
  const context = useContext(SpecialPassContext);
  if (context === undefined) {
    throw new Error('useSpecialPass must be used within a SpecialPassProvider');
  }
  return context;
}
