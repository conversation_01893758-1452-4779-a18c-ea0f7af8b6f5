import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Save, X, Users, AlertCircle } from "lucide-react";
import { toast } from 'sonner';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface Student {
  id: string;
  name: string;
  admission_number: string;
  email: string | null;
  hostel: string | null;
  route: string | null;
  created_at: string;
  updated_at: string;
}

interface StudentModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  onSubmit: (student: Student) => Promise<void>;
  editingStudent?: Student | null;
  formLoading: boolean;
  routeOptions: { route_code: string; bus_name: string }[];
  hostelOptions: string[];
}

interface ValidationErrors {
  name?: string;
  admission_number?: string;
  email?: string;
}

const StudentModal: React.FC<StudentModalProps> = ({
  open,
  setOpen,
  onSubmit,
  editingStudent,
  formLoading,
  routeOptions,
  hostelOptions
}) => {
  const [formData, setFormData] = useState<Student>({
    id: '',
    name: '',
    admission_number: '',
    email: '',
    hostel: '',
    route: '',
    created_at: '',
    updated_at: ''
  });
  const [errors, setErrors] = useState<ValidationErrors>({});

  useEffect(() => {
    if (editingStudent) {
      setFormData({
        id: editingStudent.id,
        name: editingStudent.name,
        admission_number: editingStudent.admission_number,
        email: editingStudent.email || '',
        hostel: editingStudent.hostel || '',
        route: editingStudent.route || '',
        created_at: editingStudent.created_at || '',
        updated_at: editingStudent.updated_at || ''
      });
    } else {
      setFormData({
        id: '',
        name: '',
        admission_number: '',
        email: '',
        hostel: '',
        route: '',
        created_at: '',
        updated_at: ''
      });
    }
  }, [editingStudent]);

  const validateForm = (): boolean => {
    const newErrors: ValidationErrors = {};
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }
    if (!formData.admission_number.trim()) {
      newErrors.admission_number = 'Admission number is required';
    }
    if (formData.email && !formData.email.includes('@')) {
      newErrors.email = 'Email must contain @ character';
    } else if (formData.email && !formData.email.endsWith('sjcetpalai.ac.in')) {
      newErrors.email = 'Email must end with sjcetpalai.ac.in';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      await onSubmit(formData);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            {editingStudent ? 'Edit Student' : 'Add New Student'}
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="name">Name *</Label>
            <Input
              id="name"
              value={formData.name ?? ""}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="Enter student name"
              required
            />
            {errors.name && (
              <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                <AlertCircle className="w-3 h-3" />
                {errors.name}
              </p>
            )}
          </div>
          <div>
            <Label htmlFor="admission_number">Admission Number *</Label>
            <Input
              id="admission_number"
              value={formData.admission_number ?? ""}
              onChange={(e) => {
                // Convert to uppercase
                const value = e.target.value.toUpperCase();
                // Allow typing by only validating when the field is complete
                // This accepts input during typing but ensures final format is correct
                if (value.length <= 9) {
                  setFormData(prev => ({ ...prev, admission_number: value }));
                  
                  // Validate completed input against required format
                  if (value.length === 9) {
                    const regex = /^\d{2}[A-Za-z]{2,4}\d{3}$/;
                    if (!regex.test(value)) {
                      toast.error('Invalid admission number format. Use 00XX000, 00XXX000, or 00XXXX000 format.');
                    }
                  }
                }
              }}
              placeholder="Enter admission number (e.g., 00XX000)"
              maxLength={9}
              required
            />
            {errors.admission_number && (
              <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                <AlertCircle className="w-3 h-3" />
                {errors.admission_number}
              </p>
            )}
          </div>
          <div>
            <Label htmlFor="email">College Email *</Label>
            <Input
              id="email"
              type="email"
              value={formData.email ?? ""}
              onChange={(e) => setFormData({ ...formData, email: e.target.value.toLowerCase() })}
              placeholder="Enter email (e.g. <EMAIL>)"
              required
            />
            {errors.email && (
              <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                <AlertCircle className="w-3 h-3" />
                {errors.email}
              </p>
            )}
          </div>
          <div>
            <Label htmlFor="hostel">Hostel</Label>
            <Select
              value={formData.hostel ?? undefined}
              onValueChange={(value) => setFormData({ ...formData, hostel: value })}
            >
              <SelectTrigger id="hostel">
                <SelectValue placeholder="Select a hostel" />
              </SelectTrigger>
              <SelectContent>
                {hostelOptions.map((hostel) => (
                  <SelectItem key={hostel} value={hostel}>
                    {hostel}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="route">Route</Label>
            <Select
              value={formData.route ?? undefined}
              onValueChange={(value) => setFormData({ ...formData, route: value })}
            >
              <SelectTrigger id="route">
                <SelectValue placeholder="Select a route" />
              </SelectTrigger>
              <SelectContent>
                {routeOptions.length > 0 ? (
                  routeOptions.map((route) => (
                    <SelectItem key={route.route_code} value={route.route_code}>
                      {route.route_code} - {route.bus_name}
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="" disabled>
                    No active routes available
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>
          <div className="flex gap-2 pt-4">
            <Button type="submit" disabled={formLoading} className="bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700">
              <Save className="w-4 h-4 mr-2" />
              {formLoading ? 'Saving...' : (editingStudent ? 'Update Student' : 'Add Student')}
            </Button>
            <Button type="button" variant="outline" onClick={() => setOpen(false)}>
              <X className="w-4 h-4 mr-2" />
              Cancel
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default StudentModal;
