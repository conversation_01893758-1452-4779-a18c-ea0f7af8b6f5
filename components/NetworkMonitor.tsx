'use client';

import { useEffect } from 'react';
import { initializeNetworkMonitoring } from '@/lib/network-monitor';

/**
 * NetworkMonitor component that initializes global network delay monitoring
 * This component should be included once in the app layout to enable monitoring
 */
export function NetworkMonitor() {
  useEffect(() => {
    // Initialize network monitoring when component mounts
    initializeNetworkMonitoring();
    
    // Cleanup function (optional - monitoring will persist across page changes)
    return () => {
      // We don't disable monitoring on unmount because we want it to persist
      // across page navigation. Only disable if you want to stop monitoring entirely.
      // disableNetworkMonitoring();
    };
  }, []);

  // This component doesn't render anything visible
  return null;
}

export default NetworkMonitor;
